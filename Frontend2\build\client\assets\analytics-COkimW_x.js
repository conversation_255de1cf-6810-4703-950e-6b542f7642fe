function nt(e,t,n,r,o){for(t=t.split?t.split("."):t,r=0;r<t.length;r++)e=e?e[t[r]]:o;return e===o?n:e}var je="undefined",rt="object",kt="any",At="*",re="__",Ce=typeof process<"u"?process:{};Ce.env&&Ce.env.NODE_ENV;var X=typeof document<"u";Ce.versions!=null&&Ce.versions.node!=null;typeof Deno<"u"&&Deno.core;X&&window.name==="nodejs"||typeof navigator<"u"&&navigator.userAgent!==void 0&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom"));function Tt(e,t){return t.charAt(0)[e]()+t.slice(1)}var Zt=Tt.bind(null,"toUpperCase"),Yt=Tt.bind(null,"toLowerCase");function en(e){return $t(e)?Zt("null"):typeof e=="object"?on(e):Object.prototype.toString.call(e).slice(8,-1)}function De(e,t){t===void 0&&(t=!0);var n=en(e);return t?Yt(n):n}function Ee(e,t){return typeof t===e}var K=Ee.bind(null,"function"),ce=Ee.bind(null,"string"),oe=Ee.bind(null,"undefined"),tn=Ee.bind(null,"boolean");Ee.bind(null,"symbol");function $t(e){return e===null}function nn(e){return De(e)==="number"&&!isNaN(e)}function rn(e){return De(e)==="array"}function B(e){if(!an(e))return!1;for(var t=e;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function an(e){return e&&(typeof e=="object"||e!==null)}function on(e){return K(e.constructor)?e.constructor.name:null}function un(e){return e instanceof Error||ce(e.message)&&e.constructor&&nn(e.constructor.stackTraceLimit)}function Ct(e,t){if(typeof t!="object"||$t(t))return!1;if(t instanceof e)return!0;var n=De(new e(""));if(un(t))for(;t;){if(De(t)===n)return!0;t=Object.getPrototypeOf(t)}return!1}Ct.bind(null,TypeError);Ct.bind(null,SyntaxError);function Le(e,t){var n=e instanceof Element||e instanceof HTMLDocument;return n&&t?cn(e,t):n}function cn(e,t){return t===void 0&&(t=""),e&&e.nodeName===t.toUpperCase()}function Me(e){var t=[].slice.call(arguments,1);return function(){return e.apply(void 0,[].slice.call(arguments).concat(t))}}Me(Le,"form");Me(Le,"button");Me(Le,"input");Me(Le,"select");function at(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch{return null}}function sn(){if(X){var e=navigator,t=e.languages;return e.userLanguage||(t&&t.length?t[0]:e.language)}}function ln(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch{}}function fn(e){return function(t){for(var n,r=Object.create(null),o=/([^&=]+)=?([^&]*)/g;n=o.exec(t);){var i=at(n[1]),c=at(n[2]);if(i)if(i.substring(i.length-2)==="[]"){var s=r[i=i.substring(0,i.length-2)]||(r[i]=[]);r[i]=Array.isArray(s)?s:[],r[i].push(c)}else r[i]=c===""||c}for(var d in r){var m=d.split("[");m.length>1&&(dn(r,m.map(function(x){return x.replace(/[?[\]\\ ]/g,"")}),r[d]),delete r[d])}return r}(function(t){return X&&window.location.search.substring(1)}())}function dn(e,t,n){for(var r=t.length-1,o=0;o<r;++o){var i=t[o];if(i==="__proto__"||i==="constructor")break;i in e||(e[i]={}),e=e[i]}e[t[r]]=n}function ze(){for(var e="",t=0,n=4294967295*Math.random()|0;t++<36;){var r="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx"[t-1],o=15&n;e+=r=="-"||r=="4"?r:(r=="x"?o:3&o|8).toString(16),n=t%8==0?4294967295*Math.random()|0:n>>4}return e}var we="global",de=re+"global"+re,pe=typeof self===rt&&self.self===self&&self||typeof global===rt&&global.global===global&&global||void 0;function ae(e){return pe[de][e]}function ie(e,t){return pe[de][e]=t}function me(e){delete pe[de][e]}function ve(e,t,n){var r;try{if(He(e)){var o=window[e];r=o[t].bind(o)}}catch{}return r||n}pe[de]||(pe[de]={});var Ne={};function He(e){if(typeof Ne[e]!==je)return Ne[e];try{var t=window[e];t.setItem(je,je),t.removeItem(je)}catch{return Ne[e]=!1}return Ne[e]=!0}function b(){return b=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},b.apply(this,arguments)}var Y="function",Q="undefined",pn="@@redux/"+Math.random().toString(36),it=function(){return typeof Symbol===Y&&Symbol.observable||"@@observable"}(),_e=" != "+Y;function Dt(e,t,n){var r;if(typeof t===Y&&typeof n===Q&&(n=t,t=void 0),typeof n!==Q){if(typeof n!==Y)throw new Error("enhancer"+_e);return n(Dt)(e,t)}if(typeof e!==Y)throw new Error("reducer"+_e);var o=e,i=t,c=[],s=c,d=!1;function m(){s===c&&(s=c.slice())}function x(){return i}function l(v){if(typeof v!==Y)throw new Error("Listener"+_e);var j=!0;return m(),s.push(v),function(){if(j){j=!1,m();var w=s.indexOf(v);s.splice(w,1)}}}function S(v){if(!B(v))throw new Error("Act != obj");if(typeof v.type===Q)throw new Error("ActType "+Q);if(d)throw new Error("Dispatch in reducer");try{d=!0,i=o(i,v)}finally{d=!1}for(var j=c=s,w=0;w<j.length;w++)(0,j[w])();return v}return S({type:"@@redux/INIT"}),(r={dispatch:S,subscribe:l,getState:x,replaceReducer:function(v){if(typeof v!==Y)throw new Error("next reducer"+_e);o=v,S({type:"@@redux/INIT"})}})[it]=function(){var v,j=l;return(v={subscribe:function(w){if(typeof w!="object")throw new TypeError("Observer != obj");function y(){w.next&&w.next(x())}return y(),{unsubscribe:j(y)}}})[it]=function(){return this},v},r}function gn(e,t){var n=t&&t.type;return"action "+(n&&n.toString()||"?")+"reducer "+e+" returns "+Q}function ge(){var e=[].slice.call(arguments);return e.length===0?function(t){return t}:e.length===1?e[0]:e.reduce(function(t,n){return function(){return t(n.apply(void 0,[].slice.call(arguments)))}})}function mn(){var e=arguments;return function(t){return function(n,r,o){var i,c=t(n,r,o),s=c.dispatch,d={getState:c.getState,dispatch:function(m){return s(m)}};return i=[].slice.call(e).map(function(m){return m(d)}),b({},c,{dispatch:s=ge.apply(void 0,i)(c.dispatch)})}}}var ee=re+"anon_id",ne=re+"user_id",se=re+"user_traits",Z="userId",ue="anonymousId",$e=["bootstrap","params","campaign","initializeStart","initialize","initializeEnd","ready","resetStart","reset","resetEnd","pageStart","page","pageEnd","pageAborted","trackStart","track","trackEnd","trackAborted","identifyStart","identify","identifyEnd","identifyAborted","userIdChanged","registerPlugins","enablePlugin","disablePlugin","online","offline","setItemStart","setItem","setItemEnd","setItemAborted","removeItemStart","removeItem","removeItemEnd","removeItemAborted"],We=["name","EVENTS","config","loaded"],h=$e.reduce(function(e,t){return e[t]=t,e},{registerPluginType:function(e){return"registerPlugin:"+e},pluginReadyType:function(e){return"ready:"+e}}),ot=/^utm_/,ut=/^an_prop_/,ct=/^an_trait_/;function vn(e){var t=e.storage.setItem;return function(n){return function(r){return function(o){if(o.type===h.bootstrap){var i=o.params,c=o.user,s=o.persistedUser,d=o.initialUser,m=s.userId===c.userId;s.anonymousId!==c.anonymousId&&t(ee,c.anonymousId),m||t(ne,c.userId),d.traits&&t(se,b({},m&&s.traits?s.traits:{},d.traits));var x=Object.keys(o.params);if(x.length){var l=i.an_uid,S=i.an_event,v=x.reduce(function(j,w){if(w.match(ot)||w.match(/^(d|g)clid/)){var y=w.replace(ot,"");j.campaign[y==="campaign"?"name":y]=i[w]}return w.match(ut)&&(j.props[w.replace(ut,"")]=i[w]),w.match(ct)&&(j.traits[w.replace(ct,"")]=i[w]),j},{campaign:{},props:{},traits:{}});n.dispatch(b({type:h.params,raw:i},v,l?{userId:l}:{})),l&&setTimeout(function(){return e.identify(l,v.traits)},0),S&&setTimeout(function(){return e.track(S,v.props)},0),Object.keys(v.campaign).length&&n.dispatch({type:h.campaign,campaign:v.campaign})}}return r(o)}}}}function yn(e){return function(t,n){if(t===void 0&&(t={}),n===void 0&&(n={}),n.type===h.setItemEnd){if(n.key===ee)return b({},t,{anonymousId:n.value});if(n.key===ne)return b({},t,{userId:n.value})}switch(n.type){case h.identify:return Object.assign({},t,{userId:n.userId,traits:b({},t.traits,n.traits)});case h.reset:return[ne,ee,se].forEach(function(r){e.removeItem(r)}),Object.assign({},t,{userId:null,anonymousId:null,traits:{}});default:return t}}}function st(e){return{userId:e.getItem(ne),anonymousId:e.getItem(ee),traits:e.getItem(se)}}var Xe=function(e){return re+"TEMP"+re+e};function hn(e){var t=e.storage,n=t.setItem,r=t.removeItem,o=t.getItem;return function(i){return function(c){return function(s){var d=s.userId,m=s.traits,x=s.options;if(s.type===h.reset&&([ne,se,ee].forEach(function(v){r(v)}),[Z,ue,"traits"].forEach(function(v){me(Xe(v))})),s.type===h.identify){o(ee)||n(ee,ze());var l=o(ne),S=o(se)||{};l&&l!==d&&i.dispatch({type:h.userIdChanged,old:{userId:l,traits:S},new:{userId:d,traits:m},options:x}),d&&n(ne,d),m&&n(se,b({},S,m))}return c(s)}}}}var be={};function lt(e,t){be[e]&&K(be[e])&&(be[e](t),delete be[e])}function Lt(e,t,n){return new Promise(function(r,o){return t()?r(e):n<1?o(b({},e,{queue:!0})):new Promise(function(i){return setTimeout(i,10)}).then(function(i){return Lt(e,t,n-10).then(r,o)})})}function bn(e){return{abort:e}}function Mt(e,t,n){var r={},o=t(),i=e.getState(),c=i.plugins,s=i.queue,d=i.user;if(!i.context.offline&&s&&s.actions&&s.actions.length){var m=s.actions.reduce(function(l,S,v){return c[S.plugin].loaded?(l.process.push(S),l.processIndex.push(v)):(l.requeue.push(S),l.requeueIndex.push(v)),l},{processIndex:[],process:[],requeue:[],requeueIndex:[]});if(m.processIndex&&m.processIndex.length){m.processIndex.forEach(function(l){var S=s.actions[l],v=S.plugin,j=S.payload.type,w=o[v][j];if(w&&K(w)){var y,E=function(p,f){return p===void 0&&(p={}),f===void 0&&(f={}),[Z,ue].reduce(function(D,N){return p.hasOwnProperty(N)&&f[N]&&f[N]!==p[N]&&(D[N]=f[N]),D},p)}(S.payload,d),C=r[E.meta.rid];if(!C&&(y=w({payload:E,config:c[v].config,instance:n,abort:bn}))&&B(y)&&y.abort)return void(r[E.meta.rid]=!0);if(!C){var k=j+":"+v;e.dispatch(b({},E,{type:k,_:{called:k,from:"queueDrain"}}))}}});var x=s.actions.filter(function(l,S){return!~m.processIndex.indexOf(S)});s.actions=x}}}var Re=function(e){var t=e.data,n=e.action,r=e.instance,o=e.state,i=e.allPlugins,c=e.allMatches,s=e.store,d=e.EVENTS;try{var m=o.plugins,x=o.context,l=n.type,S=l.match(le),v=t.exact.map(function(y){return y.pluginName});S&&(v=c.during.map(function(y){return y.pluginName}));var j=function(y,E){return function(C,k,p){var f=k.config,D=k.name,N=D+"."+C.type;p&&(N=p.event);var T=C.type.match(le)?function(M,L,F,z,G){return function(q,I){var R=z?z.name:M,J=I&&Oe(I)?I:F;if(z&&(!(J=I&&Oe(I)?I:[M]).includes(M)||J.length!==1))throw new Error("Method "+L+" can only abort "+M+" plugin. "+JSON.stringify(J)+" input valid");return b({},G,{abort:{reason:q,plugins:J,caller:L,_:R}})}}(D,N,E,p,C):function(M,L){return function(){throw new Error(M.type+" action not cancellable. Remove abort in "+L)}}(C,N);return{payload:wn(C),instance:y,config:f||{},abort:T}}}(r,v),w=t.exact.reduce(function(y,E){var C=E.pluginName,k=E.methodName,p=!1;return k.match(/^initialize/)||k.match(/^reset/)||(p=!m[C].loaded),x.offline&&k.match(/^(page|track|identify)/)&&(p=!0),y[""+C]=p,y},{});return Promise.resolve(t.exact.reduce(function(y,E,C){try{var k=E.pluginName;return Promise.resolve(y).then(function(p){function f(){return Promise.resolve(p)}var D=function(){if(t.namespaced&&t.namespaced[k])return Promise.resolve(t.namespaced[k].reduce(function(N,T,M){try{return Promise.resolve(N).then(function(L){return T.method&&K(T.method)?(function(q,I){var R=yt(q);if(R&&R.name===I){var J=yt(R.method);throw new Error([I+" plugin is calling method "+q,"Plugins cant call self","Use "+R.method+" "+(J?"or "+J.method:"")+" in "+I+" plugin insteadof "+q].join(`
`))}}(T.methodName,T.pluginName),Promise.resolve(T.method({payload:L,instance:r,abort:(F=L,z=k,G=T.pluginName,function(q,I){return b({},F,{abort:{reason:q,plugins:I||[z],caller:l,from:G||z}})}),config:pt(T.pluginName,m,i),plugins:m})).then(function(q){var I=B(q)?q:{};return Promise.resolve(b({},L,I))})):L;var F,z,G})}catch(L){return Promise.reject(L)}},Promise.resolve(n))).then(function(N){p[k]=N});p[k]=n}();return D&&D.then?D.then(f):f()})}catch(p){return Promise.reject(p)}},Promise.resolve({}))).then(function(y){return Promise.resolve(t.exact.reduce(function(E,C,k){try{var p=t.exact.length===k+1,f=C.pluginName,D=i[f];return Promise.resolve(E).then(function(N){var T=y[f]?y[f]:{};if(S&&(T=N),Be(T,f))return Je({data:T,method:l,instance:r,pluginName:f,store:s}),Promise.resolve(N);if(Be(N,f))return p&&Je({data:N,method:l,instance:r,store:s}),Promise.resolve(N);if(w.hasOwnProperty(f)&&w[f]===!0)return s.dispatch({type:"queue",plugin:f,payload:T,_:{called:"queue",from:"queueMechanism"}}),Promise.resolve(N);var M=j(y[f],i[f]);return Promise.resolve(D[l]({abort:M.abort,payload:T,instance:r,config:pt(f,m,i),plugins:m})).then(function(L){var F=B(L)?L:{},z=b({},N,F),G=y[f];if(Be(G,f))Je({data:G,method:l,instance:r,pluginName:f,store:s});else{var q=l+":"+f;(q.match(/:/g)||[]).length<2&&!l.match(ft)&&!l.match(dt)&&r.dispatch(b({},S?z:T,{type:q,_:{called:q,from:"submethod"}}))}return Promise.resolve(z)})})}catch(N){return Promise.reject(N)}},Promise.resolve(n))).then(function(E){if(!(l.match(le)||l.match(/^registerPlugin/)||l.match(dt)||l.match(ft)||l.match(/^params/)||l.match(/^userIdChanged/))){if(d.plugins.includes(l),E._&&E._.originalAction===l)return E;var C=b({},E,{_:{originalAction:E.type,called:E.type,from:"engineEnd"}});zt(E,t.exact.length)&&!l.match(/End$/)&&(C=b({},C,{type:E.type+"Aborted"})),s.dispatch(C)}return E})})}catch(y){return Promise.reject(y)}},le=/Start$/,ft=/^bootstrap/,dt=/^ready/;function Je(e){var t=e.pluginName,n=e.method+"Aborted"+(t?":"+t:"");e.store.dispatch(b({},e.data,{type:n,_:{called:n,from:"abort"}}))}function pt(e,t,n){var r=t[e]||n[e];return r&&r.config?r.config:{}}function gt(e,t){return t.reduce(function(n,r){return r[e]?n.concat({methodName:e,pluginName:r.name,method:r[e]}):n},[])}function mt(e,t){var n=e.replace(le,""),r=t?":"+t:"";return[""+e+r,""+n+r,n+"End"+r]}function Be(e,t){var n=e.abort;return!!n&&(n===!0||vt(n,t)||n&&vt(n.plugins,t))}function zt(e,t){var n=e.abort;if(!n)return!1;if(n===!0||ce(n))return!0;var r=n.plugins;return Oe(n)&&n.length===t||Oe(r)&&r.length===t}function Oe(e){return Array.isArray(e)}function vt(e,t){return!(!e||!Oe(e))&&e.includes(t)}function yt(e){var t=e.match(/(.*):(.*)/);return!!t&&{method:t[1],name:t[2]}}function wn(e){return Object.keys(e).reduce(function(t,n){return n==="type"||(t[n]=B(e[n])?Object.assign({},e[n]):e[n]),t},{})}function In(e,t,n){var r={};return function(o){return function(i){return function(c){try{var s,d=function(p){return s?p:i(l)},m=c.type,x=c.plugins,l=c;if(c.abort)return Promise.resolve(i(c));if(m===h.enablePlugin&&o.dispatch({type:h.initializeStart,plugins:x,disabled:[],fromEnable:!0,meta:c.meta}),m===h.disablePlugin&&setTimeout(function(){return lt(c.meta.rid,{payload:c})},0),m===h.initializeEnd){var S=t(),v=Object.keys(S),j=v.filter(function(p){return x.includes(p)}).map(function(p){return S[p]}),w=[],y=[],E=c.disabled,C=j.map(function(p){var f=p.loaded,D=p.name,N=p.config;return Lt(p,function(){return f({config:N})},1e4).then(function(T){return r[D]||(o.dispatch({type:h.pluginReadyType(D),name:D,events:Object.keys(p).filter(function(M){return!We.includes(M)})}),r[D]=!0),w=w.concat(D),p}).catch(function(T){if(T instanceof Error)throw new Error(T);return y=y.concat(T.name),T})});Promise.all(C).then(function(p){var f={plugins:w,failed:y,disabled:E};setTimeout(function(){v.length===C.length+E.length&&o.dispatch(b({},{type:h.ready},f))},0)})}var k=function(){if(m!==h.bootstrap)return/^ready:([^:]*)$/.test(m)&&setTimeout(function(){return Mt(o,t,e)},0),Promise.resolve(function(p,f,D,N,T){try{var M=K(f)?f():f,L=p.type,F=L.replace(le,"");if(p._&&p._.called)return Promise.resolve(p);var z=D.getState(),G=(R=M,(J=z.plugins)===void 0&&(J={}),(ye=p.options)===void 0&&(ye={}),Object.keys(R).filter(function(a){var u=ye.plugins||{};return tn(u[a])?u[a]:u.all!==!1&&(!J[a]||J[a].enabled!==!1)}).map(function(a){return R[a]}));L===h.initializeStart&&p.fromEnable&&(G=Object.keys(z.plugins).filter(function(a){var u=z.plugins[a];return p.plugins.includes(a)&&!u.initialized}).map(function(a){return M[a]}));var q=G.map(function(a){return a.name}),I=function(a,u,g){var P=mt(a).map(function(O){return gt(O,u)});return u.reduce(function(O,_){var V=_.name,$=mt(a,V).map(function(he){return gt(he,u)}),U=$[0],A=$[1],H=$[2];return U.length&&(O.beforeNS[V]=U),A.length&&(O.duringNS[V]=A),H.length&&(O.afterNS[V]=H),O},{before:P[0],beforeNS:{},during:P[1],duringNS:{},after:P[2],afterNS:{}})}(L,G);return Promise.resolve(Re({action:p,data:{exact:I.before,namespaced:I.beforeNS},state:z,allPlugins:M,allMatches:I,instance:D,store:N,EVENTS:T})).then(function(a){function u(){var O=function(){if(L.match(le))return Promise.resolve(Re({action:b({},g,{type:F+"End"}),data:{exact:I.after,namespaced:I.afterNS},state:z,allPlugins:M,allMatches:I,instance:D,store:N,EVENTS:T})).then(function(_){_.meta&&_.meta.hasCallback&&lt(_.meta.rid,{payload:_})})}();return O&&O.then?O.then(function(){return a}):a}if(zt(a,q.length))return a;var g,P=function(){if(L!==F)return Promise.resolve(Re({action:b({},a,{type:F}),data:{exact:I.during,namespaced:I.duringNS},state:z,allPlugins:M,allMatches:I,instance:D,store:N,EVENTS:T})).then(function(O){g=O});g=a}();return P&&P.then?P.then(u):u()})}catch(a){return Promise.reject(a)}var R,J,ye}(c,t,e,o,n)).then(function(p){return s=1,i(p)})}();return Promise.resolve(k&&k.then?k.then(d):d(k))}catch(p){return Promise.reject(p)}}}}}function Sn(e){return function(t){return function(n){return function(r){var o=r.type,i=r.key,c=r.value,s=r.options;if(o===h.setItem||o===h.removeItem){if(r.abort)return n(r);o===h.setItem?e.setItem(i,c,s):e.removeItem(i,s)}return n(r)}}}}var Pn=function(){var e=this;this.before=[],this.after=[],this.addMiddleware=function(t,n){e[n]=e[n].concat(t)},this.removeMiddleware=function(t,n){var r=e[n].findIndex(function(o){return o===t});r!==-1&&(e[n]=[].concat(e[n].slice(0,r),e[n].slice(r+1)))},this.dynamicMiddlewares=function(t){return function(n){return function(r){return function(o){var i={getState:n.getState,dispatch:function(s){return n.dispatch(s)}},c=e[t].map(function(s){return s(i)});return ge.apply(void 0,c)(r)(o)}}}}};function On(e){return function(t,n){t===void 0&&(t={});var r={};if(n.type==="initialize:aborted")return t;if(/^registerPlugin:([^:]*)$/.test(n.type)){var o=ht(n.type,"registerPlugin"),i=e()[o];if(!i||!o)return t;var c=n.enabled,s=i.config;return r[o]={enabled:c,initialized:!!c&&!i.initialize,loaded:!!c&&!!i.loaded({config:s}),config:s},b({},t,r)}if(/^initialize:([^:]*)$/.test(n.type)){var d=ht(n.type,h.initialize),m=e()[d];return m&&d?(r[d]=b({},t[d],{initialized:!0,loaded:!!m.loaded({config:m.config})}),b({},t,r)):t}if(/^ready:([^:]*)$/.test(n.type))return r[n.name]=b({},t[n.name],{loaded:!0}),b({},t,r);switch(n.type){case h.disablePlugin:return b({},t,bt(n.plugins,!1,t));case h.enablePlugin:return b({},t,bt(n.plugins,!0,t));default:return t}}}function ht(e,t){return e.substring(t.length+1,e.length)}function bt(e,t,n){return e.reduce(function(r,o){return r[o]=b({},n[o],{enabled:t}),r},n)}function Ut(e){try{return JSON.parse(JSON.stringify(e))}catch{}return e}var En={last:{},history:[]};function xn(e,t){e===void 0&&(e=En);var n=t.options,r=t.meta;if(t.type===h.track){var o=Ut(b({event:t.event,properties:t.properties},Object.keys(n).length&&{options:n},{meta:r}));return b({},e,{last:o,history:e.history.concat(o)})}return e}var jn={actions:[]};function Nn(e,t){e===void 0&&(e=jn);var n=t.payload;switch(t.type){case"queue":var r;return r=n&&n.type&&n.type===h.identify?[t].concat(e.actions):e.actions.concat(t),b({},e,{actions:r});case"dequeue":return[];default:return e}}var qt=/#.*$/;function _n(e){var t=/(http[s]?:\/\/)?([^\/\s]+\/)(.*)/g.exec(e);return"/"+(t&&t[3]?t[3].split("?")[0].replace(qt,""):"")}var Vt,Rt,Jt,Bt,kn=function(e){if(e===void 0&&(e={}),!X)return e;var t=document,n=t.title,r=t.referrer,o=window,i=o.location,c=o.innerWidth,s=o.innerHeight,d=i.hash,m=i.search,x=function(S){var v=function(){if(X){for(var j,w=document.getElementsByTagName("link"),y=0;j=w[y];y++)if(j.getAttribute("rel")==="canonical")return j.getAttribute("href")}}();return v?v.match(/\?/)?v:v+S:window.location.href.replace(qt,"")}(m),l={title:n,url:x,path:_n(x),hash:d,search:m,width:c,height:s};return r&&r!==""&&(l.referrer=r),b({},l,e)},An={last:{},history:[]};function Tn(e,t){e===void 0&&(e=An);var n=t.options;if(t.type===h.page){var r=Ut(b({properties:t.properties,meta:t.meta},Object.keys(n).length&&{options:n}));return b({},e,{last:r,history:e.history.concat(r)})}return e}Vt=function(){if(!X)return!1;var e=navigator.appVersion;return~e.indexOf("Win")?"Windows":~e.indexOf("Mac")?"MacOS":~e.indexOf("X11")?"UNIX":~e.indexOf("Linux")?"Linux":"Unknown OS"}(),Rt=X?document.referrer:null,Jt=sn(),Bt=ln();var wt={initialized:!1,sessionId:ze(),app:null,version:null,debug:!1,offline:!!X&&!navigator.onLine,os:{name:Vt},userAgent:X?navigator.userAgent:"node",library:{name:"analytics",version:"0.12.16"},timezone:Bt,locale:Jt,campaign:{},referrer:Rt};function $n(e,t){e===void 0&&(e=wt);var n=e.initialized,r=t.campaign;switch(t.type){case h.campaign:return b({},e,{campaign:r});case h.offline:return b({},e,{offline:!0});case h.online:return b({},e,{offline:!1});default:return n?e:b({},wt,e,{initialized:!0})}}var Cn=["plugins","reducers","storage"];function Dn(e,t,n){if(X){var r=window[(n?"add":"remove")+"EventListener"];e.split(" ").forEach(function(o){r(o,t)})}}function Ln(e){var t=Dn.bind(null,"online offline",function(n){return Promise.resolve(!navigator.onLine).then(e)});return t(!0),function(n){return t(!1)}}function Ft(){return ie("analytics",[]),function(e){return function(t,n,r){var o=e(t,n,r),i=o.dispatch;return Object.assign(o,{dispatch:function(c){return pe[de].analytics.push(c.action||c),i(c)}})}}}function It(e){return function(){return ge(ge.apply(null,arguments),Ft())}}function Fe(e){return e?rn(e)?e:[e]:[]}function St(e,t,n){e===void 0&&(e={});var r,o,i=ze();return t&&(be[i]=(r=t,o=function(c){for(var s,d=c||Array.prototype.slice.call(arguments),m=0;m<d.length;m++)if(K(d[m])){s=d[m];break}return s}(n),function(c){o&&o(c),r(c)})),b({},e,{rid:i,ts:new Date().getTime()},t?{hasCallback:!0}:{})}function Mn(e){e===void 0&&(e={});var t=e.reducers||{},n=e.initialUser||{},r=(e.plugins||[]).reduce(function(a,u){if(K(u))return a.middlewares=a.middlewares.concat(u),a;if(u.NAMESPACE&&(u.name=u.NAMESPACE),!u.name)throw new Error("https://lytics.dev/errors/1");u.config||(u.config={});var g=u.EVENTS?Object.keys(u.EVENTS).map(function(_){return u.EVENTS[_]}):[];a.pluginEnabled[u.name]=!(u.enabled===!1||u.config.enabled===!1),delete u.enabled,u.methods&&(a.methods[u.name]=Object.keys(u.methods).reduce(function(_,V){var $;return _[V]=($=u.methods[V],function(){for(var U=Array.prototype.slice.call(arguments),A=new Array($.length),H=0;H<U.length;H++)A[H]=U[H];return A[A.length]=f,$.apply({instance:f},A)}),_},{}),delete u.methods);var P=Object.keys(u).concat(g),O=new Set(a.events.concat(P));if(a.events=Array.from(O),a.pluginsArray=a.pluginsArray.concat(u),a.plugins[u.name])throw new Error(u.name+"AlreadyLoaded");return a.plugins[u.name]=u,a.plugins[u.name].loaded||(a.plugins[u.name].loaded=function(){return!0}),a},{plugins:{},pluginEnabled:{},methods:{},pluginsArray:[],middlewares:[],events:[]}),o=e.storage?e.storage:{getItem:ae,setItem:ie,removeItem:me},i=function(a){return function(u,g,P){return g.getState("user")[u]||(P&&B(P)&&P[u]?P[u]:st(a)[u]||ae(Xe(u))||null)}}(o),c=r.plugins,s=r.events.filter(function(a){return!We.includes(a)}).sort(),d=new Set(s.concat($e).filter(function(a){return!We.includes(a)})),m=Array.from(d).sort(),x=function(){return c},l=new Pn,S=l.addMiddleware,v=l.removeMiddleware,j=l.dynamicMiddlewares,w=function(){throw new Error("Abort disabled inListener")},y=fn(),E=st(o),C=b({},E,n,y.an_uid?{userId:y.an_uid}:{},y.an_aid?{anonymousId:y.an_aid}:{});C.anonymousId||(C.anonymousId=ze());var k=b({enable:function(a,u){return new Promise(function(g){I.dispatch({type:h.enablePlugin,plugins:Fe(a),_:{originalAction:h.enablePlugin}},g,[u])})},disable:function(a,u){return new Promise(function(g){I.dispatch({type:h.disablePlugin,plugins:Fe(a),_:{originalAction:h.disablePlugin}},g,[u])})}},r.methods),p=!1,f={identify:function(a,u,g,P){try{var O=ce(a)?a:null,_=B(a)?a:u,V=g||{},$=f.user();ie(Xe(Z),O);var U=O||_.userId||i(Z,f,_);return Promise.resolve(new Promise(function(A){I.dispatch(b({type:h.identifyStart,userId:U,traits:_||{},options:V,anonymousId:$.anonymousId},$.id&&$.id!==O&&{previousId:$.id}),A,[u,g,P])}))}catch(A){return Promise.reject(A)}},track:function(a,u,g,P){try{var O=B(a)?a.event:a;if(!O||!ce(O))throw new Error("EventMissing");var _=B(a)?a:u||{},V=B(g)?g:{};return Promise.resolve(new Promise(function($){I.dispatch({type:h.trackStart,event:O,properties:_,options:V,userId:i(Z,f,u),anonymousId:i(ue,f,u)},$,[u,g,P])}))}catch($){return Promise.reject($)}},page:function(a,u,g){try{var P=B(a)?a:{},O=B(u)?u:{};return Promise.resolve(new Promise(function(_){I.dispatch({type:h.pageStart,properties:kn(P),options:O,userId:i(Z,f,P),anonymousId:i(ue,f,P)},_,[a,u,g])}))}catch(_){return Promise.reject(_)}},user:function(a){if(a===Z||a==="id")return i(Z,f);if(a===ue||a==="anonId")return i(ue,f);var u=f.getState("user");return a?nt(u,a):u},reset:function(a){return new Promise(function(u){I.dispatch({type:h.resetStart},u,a)})},ready:function(a){return p&&a({plugins:k,instance:f}),f.on(h.ready,function(u){a(u),p=!0})},on:function(a,u){if(!a||!K(u))return!1;if(a===h.bootstrap)throw new Error(".on disabled for "+a);var g=/Start$|Start:/;if(a==="*"){var P=function($){return function(U){return function(A){return A.type.match(g)&&u({payload:A,instance:f,plugins:c}),U(A)}}},O=function($){return function(U){return function(A){return A.type.match(g)||u({payload:A,instance:f,plugins:c}),U(A)}}};return S(P,ke),S(O,Ae),function(){v(P,ke),v(O,Ae)}}var _=a.match(g)?ke:Ae,V=function($){return function(U){return function(A){return A.type===a&&u({payload:A,instance:f,plugins:c,abort:w}),U(A)}}};return S(V,_),function(){return v(V,_)}},once:function(a,u){if(!a||!K(u))return!1;if(a===h.bootstrap)throw new Error(".once disabled for "+a);var g=f.on(a,function(P){u({payload:P.payload,instance:f,plugins:c,abort:w}),g()});return g},getState:function(a){var u=I.getState();return a?nt(u,a):Object.assign({},u)},dispatch:function(a){var u=ce(a)?{type:a}:a;if($e.includes(u.type))throw new Error("reserved action "+u.type);var g=b({},u,{_:b({originalAction:u.type},a._||{})});I.dispatch(g)},enablePlugin:k.enable,disablePlugin:k.disable,plugins:k,storage:{getItem:o.getItem,setItem:function(a,u,g){I.dispatch({type:h.setItemStart,key:a,value:u,options:g})},removeItem:function(a,u){I.dispatch({type:h.removeItemStart,key:a,options:u})}},setAnonymousId:function(a,u){f.storage.setItem(ee,a,u)},events:{core:$e,plugins:s}},D=r.middlewares.concat([function(a){return function(u){return function(g){return g.meta||(g.meta=St()),u(g)}}},j(ke),In(f,x,{all:m,plugins:s}),Sn(o),vn(f),hn(f),j(Ae)]),N={context:$n,user:yn(o),page:Tn,track:xn,plugins:On(x),queue:Nn},T=ge,M=ge;if(X&&e.debug){var L=window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__;L&&(T=L({trace:!0,traceLimit:25})),M=function(){return arguments.length===0?Ft():B(typeof arguments[0])?It():It().apply(null,arguments)}}var F,z=function(a){return Object.keys(a).reduce(function(u,g){return Cn.includes(g)||(u[g]=a[g]),u},{})}(e),G=r.pluginsArray.reduce(function(a,u){var g=u.name,P=u.config,O=u.loaded,_=r.pluginEnabled[g];return a[g]={enabled:_,initialized:!!_&&!u.initialize,loaded:!!O({config:P}),config:P},a},{}),q={context:z,user:C,plugins:G},I=Dt(function(a){for(var u=Object.keys(a),g={},P=0;P<u.length;P++){var O=u[P];typeof a[O]===Y&&(g[O]=a[O])}var _,V=Object.keys(g);try{(function($){Object.keys($).forEach(function(U){var A=$[U];if(typeof A(void 0,{type:"@@redux/INIT"})===Q||typeof A(void 0,{type:pn})===Q)throw new Error("reducer "+U+" "+Q)})})(g)}catch($){_=$}return function($,U){if($===void 0&&($={}),_)throw _;for(var A=!1,H={},he=0;he<V.length;he++){var xe=V[he],tt=$[xe],Ve=(0,g[xe])(tt,U);if(typeof Ve===Q){var Qt=gn(xe,U);throw new Error(Qt)}H[xe]=Ve,A=A||Ve!==tt}return A?H:$}}(b({},N,t)),q,M(T(mn.apply(void 0,D))));I.dispatch=(F=I.dispatch,function(a,u,g){var P=b({},a,{meta:St(a.meta,u,Fe(g))});return F.apply(null,[P])});var R=Object.keys(c);I.dispatch({type:h.bootstrap,plugins:R,config:z,params:y,user:C,initialUser:n,persistedUser:E});var J=R.filter(function(a){return r.pluginEnabled[a]}),ye=R.filter(function(a){return!r.pluginEnabled[a]});return I.dispatch({type:h.registerPlugins,plugins:R,enabled:r.pluginEnabled}),r.pluginsArray.map(function(a,u){var g=a.bootstrap,P=a.config,O=a.name;g&&K(g)&&g({instance:f,config:P,payload:a}),I.dispatch({type:h.registerPluginType(O),name:O,enabled:r.pluginEnabled[O],plugin:a}),r.pluginsArray.length===u+1&&I.dispatch({type:h.initializeStart,plugins:J,disabled:ye})}),Ln(function(a){I.dispatch({type:a?h.offline:h.online})}),function(a,u,g){setInterval(function(){return Mt(a,u,g)},3e3)}(I,x,f),f}var ke="before",Ae="after",Ie="cookie",te=Xt(),Gt=Ue,zn=Ue;function Wt(e){return te?Ue(e,"",-1):me(e)}function Xt(){if(te!==void 0)return te;var e="cookiecookie";try{Ue(e,e),te=document.cookie.indexOf(e)!==-1,Wt(e)}catch{te=!1}return te}function Ue(e,t,n,r,o,i){if(typeof window<"u"){var c=arguments.length>1;return te===!1&&(c?ie(e,t):ae(e)),c?document.cookie=e+"="+encodeURIComponent(t)+(n?"; expires="+new Date(+new Date+1e3*n).toUTCString()+(r?"; path="+r:"")+(o?"; domain="+o:"")+(i?"; secure":""):""):decodeURIComponent((("; "+document.cookie).split("; "+e+"=")[1]||"").split(";")[0])}}var Se="localStorage",Un=He.bind(null,"localStorage");ve("localStorage","getItem",ae);ve("localStorage","setItem",ie);ve("localStorage","removeItem",me);var Pe="sessionStorage",qn=He.bind(null,"sessionStorage");ve("sessionStorage","getItem",ae);ve("sessionStorage","setItem",ie);ve("sessionStorage","removeItem",me);function fe(e){var t=e;try{if((t=JSON.parse(e))==="true")return!0;if(t==="false")return!1;if(B(t))return t;parseFloat(t)===t&&(t=parseFloat(t))}catch{}if(t!==null&&t!=="")return t}var Vn=Un(),Rn=qn(),Jn=Xt();function Ht(e,t){if(e){var n=Ke(t),r=!et(n),o=Qe(n)?fe(localStorage.getItem(e)):void 0;if(r&&!oe(o))return o;var i=Ze(n)?fe(Gt(e)):void 0;if(r&&i)return i;var c=Ye(n)?fe(sessionStorage.getItem(e)):void 0;if(r&&c)return c;var s=ae(e);return r?s:{localStorage:o,sessionStorage:c,cookie:i,global:s}}}function Bn(e,t,n){if(e&&!oe(t)){var r={},o=Ke(n),i=JSON.stringify(t),c=!et(o);return Qe(o)&&(r[Se]=Te(Se,t,fe(localStorage.getItem(e))),localStorage.setItem(e,i),c)?r[Se]:Ze(o)&&(r[Ie]=Te(Ie,t,fe(Gt(e))),zn(e,i),c)?r[Ie]:Ye(o)&&(r[Pe]=Te(Pe,t,fe(sessionStorage.getItem(e))),sessionStorage.setItem(e,i),c)?r[Pe]:(r[we]=Te(we,t,ae(e)),ie(e,t),c?r[we]:r)}}function Fn(e,t){if(e){var n=Ke(t),r=Ht(e,At),o={};return!oe(r.localStorage)&&Qe(n)&&(localStorage.removeItem(e),o[Se]=r.localStorage),!oe(r.cookie)&&Ze(n)&&(Wt(e),o[Ie]=r.cookie),!oe(r.sessionStorage)&&Ye(n)&&(sessionStorage.removeItem(e),o[Pe]=r.sessionStorage),!oe(r.global)&&qe(n,we)&&(me(e),o[we]=r.global),o}}function Ke(e){return e?ce(e)?e:e.storage:kt}function Qe(e){return Vn&&qe(e,Se)}function Ze(e){return Jn&&qe(e,Ie)}function Ye(e){return Rn&&qe(e,Pe)}function et(e){return e===At||e==="all"}function qe(e,t){return e===kt||e===t||et(e)}function Te(e,t,n){return{location:e,current:t,previous:n}}var Gn={setItem:Bn,getItem:Ht,removeItem:Fn};function Wn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Pt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function Ot(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Pt(Object(n),!0).forEach(function(r){Wn(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Pt(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function Xn(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t={storage:Gn};return Mn(Ot(Ot({},t),e))}function Et(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function W(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Et(Object(n),!0).forEach(function(r){Hn(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Et(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function Hn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var xt={},jt="https://www.googletagmanager.com/gtag/js",Kt={debug_mode:!1,send_page_view:!1,anonymize_ip:!1,allow_google_signals:!0,allow_ad_personalization_signals:!0,cookie_flags:""},Kn={gtagName:"gtag",dataLayerName:"ga4DataLayer",measurementIds:[],gtagConfig:Kt};function Qn(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=0,n=Ge(e.measurementIds),r=W(W({},Kn),e);return{name:"google-analytics",config:r,initialize:function(i){var c=i.config,s=i.instance,d=c.dataLayerName,m=c.customScriptSrc,x=c.gtagName,l=c.gtagConfig,S=c.debug,v=c.nonce,j=d?"&l=".concat(d):"",w=m||"".concat(jt,"?id=").concat(n[0]).concat(j);if(!_t(w)){var y=document.createElement("script");y.async=!0,y.src=w,v&&y.setAttribute("nonce",v),document.body.appendChild(y)}window[d]||(window[d]=window[d]||[]),window[x]||(window[x]=function(){window[d].push(arguments)}),window[x]("js",new Date);var E=W(W({},Kt),l||{});S===!0?E.debug_mode=!0:delete E.debug_mode;var C=s.user()||{},k=C.traits||{};Object.keys(k).length&&window[x]("set","user_properties",k);for(var p=0;p<n.length;p++)xt[n[p]]||(window[x]("config",n[p],E),xt[n[p]]=!0)},identify:function(i){var c=i.payload,s=i.config,d=s.gtagName;!window[d]||!n.length||(c.userId&&window[d]("set",{user_id:c.userId}),Object.keys(c.traits).length&&window[d]("set","user_properties",c.traits))},page:function(i){var c=i.payload,s=i.config,d=i.instance,m=s.gtagName,x=s.gtagConfig;if(!(!window[m]||!n.length)){var l=c.properties,S=l.send_to,v=d.getState("context.campaign"),j={page_title:l.title,page_location:l.url,page_path:l.path||document.location.pathname,page_hash:l.hash,page_search:l.page_search,page_referrer:l.referrer},w=Nt(v),y=d.user("userId"),E=W(W(W(W({},S?{send_to:S}:{}),j),w),y?{user_id:y}:{});if(x&&x.send_page_view&&t===0){t++;return}window[m]("event","page_view",E),t++}},track:function(i){var c=i.payload,s=i.config,d=i.instance,m=c.properties,x=c.event,l=d.getState("context.campaign"),S=s.gtagName;if(!(!window[S]||!n.length)){var v=Nt(l),j=d.user("userId"),w=W(W(W({},m),v),j?{user_id:j}:{});window[S]("event",x,w)}},loaded:function(){var i=r.dataLayerName,c=r.customScriptSrc,s=i&&window[i]&&Array.prototype.push===window[i].push;return _t(c||jt)&&s},methods:{addTag:function(i){var c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};window[r.gtagName]&&(window[r.gtagName]("config",i,c),n&&!n.includes(i)&&(n=n.concat(i)))},disable:function(i){for(var c=i?Ge(i):n,s=0;s<n.length;s++){var d=n[s];c.includes(d)&&(window["ga-disable-".concat(d)]=!0)}},enable:function(i){for(var c=i?Ge(i):n,s=0;s<n.length;s++){var d=n[s];c.includes(d)&&(window["ga-disable-".concat(d)]=!1)}}}}}function Ge(e){if(!e)throw new Error("No GA Measurement ID defined");if(Array.isArray(e))return e;if(typeof e=="string")return[e];throw new Error("GA Measurement ID must be string or array of strings")}function Nt(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t={},n=e.id,r=e.name,o=e.source,i=e.medium,c=e.content,s=e.keyword;return n&&(t.campaignId=n),r&&(t.campaignName=r),o&&(t.campaignSource=o),i&&(t.campaignMedium=i),c&&(t.campaignContent=c),s&&(t.campaignKeyword=s),t}function _t(e){var t=document.querySelectorAll("script[src]"),n=new RegExp("^".concat(e));return!!Object.values(t).filter(function(r){return n.test(r.src)}).length}var Zn=Qn;let Yn=null;typeof window<"u"&&(Yn=Xn({app:"BetaLawvriksh-Frontend",plugins:[Zn({measurementIds:["G-TQJEZES4ET"]})]}));export{Yn as a};
