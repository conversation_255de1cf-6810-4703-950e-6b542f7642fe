import{r as b,t as U,j as e}from"./index-n_C3wGZ6.js";import{c as S,D as I,d as F,e as P,f as E}from"./hooks-B1CQDN4h.js";function O(){const[n,a]=b.useState({username:"",password:""}),{login:c,isLoading:s,error:i}=S(),x=U(),d=o=>{a({...n,[o.target.name]:o.target.value})},m=async o=>{if(o.preventDefault(),!n.username.trim()||!n.password.trim())return;await c(n)&&x("/admin")},h=()=>{a(I)};return e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-law-dark to-gray-900 px-4",children:e.jsxs("div",{className:"bg-law-cream rounded-3xl p-10 max-w-md w-full shadow-2xl relative border-4 border-transparent bg-clip-padding before:absolute before:inset-[-4px] before:bg-gold-texture before:rounded-3xl before:z-[-1] before:brightness-75 before:contrast-125",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"bg-gold-texture bg-clip-text text-transparent font-merriweather font-semibold text-3xl mb-2 bg-cover bg-center",children:"Admin Access"}),e.jsx("p",{className:"text-gray-600 font-source-sans-pro text-lg",children:"LawVriksh Dashboard"})]}),e.jsxs("form",{onSubmit:m,className:"flex flex-col gap-6",children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{htmlFor:"username",className:"font-montserrat font-medium text-base text-gray-800",children:"Username"}),e.jsx("input",{type:"text",id:"username",name:"username",value:n.username,onChange:d,className:"p-3 border-2 border-gray-300 rounded-xl text-base bg-white transition-colors focus:outline-none focus:border-law-gold",placeholder:"Enter username",required:!0})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{htmlFor:"password",className:"font-montserrat font-medium text-base text-gray-800",children:"Password"}),e.jsx("input",{type:"password",id:"password",name:"password",value:n.password,onChange:d,className:"p-3 border-2 border-gray-300 rounded-xl text-base bg-white transition-colors focus:outline-none focus:border-law-gold",placeholder:"Enter password",required:!0})]}),i&&e.jsx("div",{className:"bg-red-50 text-red-800 p-3 rounded-lg border border-red-200 text-sm text-center",children:i}),e.jsx("button",{type:"submit",className:`p-4 border-none rounded-full bg-gold-texture text-black font-montserrat font-semibold text-lg cursor-pointer transition-all hover:scale-105 hover:opacity-90 ${s?"opacity-60 cursor-not-allowed transform-none":""}`,disabled:s,children:s?"Signing in...":"Sign In"})]}),e.jsxs("div",{className:"text-center mt-6",children:[e.jsx("p",{className:"text-sm text-gray-600 leading-relaxed mb-3",children:"Enter your admin credentials to access the dashboard"}),e.jsx("button",{type:"button",onClick:h,className:"text-xs text-law-gold hover:underline",children:"Use default credentials"})]})]})})}function N({data:n,type:a,isLoading:c=!1}){const[s,i]=b.useState(1),x=10,d=Math.ceil(n.length/x),m=(s-1)*x,h=m+x,o=n.slice(m,h),l=r=>new Date(r).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),f=r=>e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Phone"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Gender"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Profession"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Registered"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:r.map(t=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:t.name}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.email}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.phone_number}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.gender||"Not specified"}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.profession||"Not specified"}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:l(t.created_at)})]},t.id))})]})}),w=r=>e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Reason"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Suggestions"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:r.map(t=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:t.name}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.email}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.not_interested_reason||"Not specified"}),e.jsx("td",{className:"px-6 py-4 text-sm text-gray-500 max-w-xs truncate",children:t.improvement_suggestions||"None provided"}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:l(t.created_at)})]},t.id))})]})}),v=r=>e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User Email"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Submitted"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:r.map(t=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["#",t.id]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.user_email||"Anonymous"}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:l(t.created_at)}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.jsx("button",{className:"text-blue-600 hover:text-blue-900",children:"View Details"})})]},t.id))})]})}),k=()=>d<=1?null:e.jsxs("div",{className:"flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6",children:[e.jsxs("div",{className:"flex flex-1 justify-between sm:hidden",children:[e.jsx("button",{onClick:()=>i(Math.max(1,s-1)),disabled:s===1,className:"relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50",children:"Previous"}),e.jsx("button",{onClick:()=>i(Math.min(d,s+1)),disabled:s===d,className:"relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50",children:"Next"})]}),e.jsxs("div",{className:"hidden sm:flex sm:flex-1 sm:items-center sm:justify-between",children:[e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-700",children:["Showing ",e.jsx("span",{className:"font-medium",children:m+1})," to"," ",e.jsx("span",{className:"font-medium",children:Math.min(h,n.length)})," of"," ",e.jsx("span",{className:"font-medium",children:n.length})," results"]})}),e.jsx("div",{children:e.jsxs("nav",{className:"isolate inline-flex -space-x-px rounded-md shadow-sm","aria-label":"Pagination",children:[e.jsx("button",{onClick:()=>i(Math.max(1,s-1)),disabled:s===1,className:"relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50",children:"Previous"}),Array.from({length:d},(r,t)=>t+1).map(r=>e.jsx("button",{onClick:()=>i(r),className:`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${r===s?"z-10 bg-law-gold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-law-gold":"text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"}`,children:r},r)),e.jsx("button",{onClick:()=>i(Math.min(d,s+1)),disabled:s===d,className:"relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50",children:"Next"})]})})]})]});return c?e.jsx("div",{className:"text-center py-12",children:e.jsx("div",{className:"text-gray-500",children:e.jsx("p",{className:"text-lg font-medium",children:"Loading..."})})}):n.length===0?e.jsx("div",{className:"text-center py-12",children:e.jsxs("div",{className:"text-gray-500",children:[e.jsx("p",{className:"text-lg font-medium",children:"No data available"}),e.jsxs("p",{className:"text-sm mt-2",children:[a==="users"&&"No user registrations yet.",a==="creators"&&"No creator registrations yet.",a==="not-interested"&&'No "not interested" responses yet.',a==="feedback"&&"No feedback submissions yet."]})]})}):e.jsxs("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:[a==="users"&&f(o),a==="creators"&&f(o),a==="not-interested"&&w(o),a==="feedback"&&v(o),k()]})}function _({onLogout:n}){var D,C,A;const[a,c]=b.useState("user-responses"),[s,i]=b.useState("user"),{users:x,creators:d,feedback:m,fetchAllData:h}=F(),{checkHealth:o}=P();b.useEffect(()=>{h()},[h]);const l={totalUsers:((D=x.data)==null?void 0:D.length)||0,totalCreators:((C=d.data)==null?void 0:C.length)||0,totalNotInterested:0,totalFeedback:((A=m.data)==null?void 0:A.length)||0},f=x.data||[],w=d.data||[],v=[],k=m.data||[],r=x.loading||d.loading||m.loading,t=()=>{console.log("Admin logout"),n()},L=async()=>{await h(),await o()},T=async u=>{try{const g=await E.exportDataJson();if(g.success&&g.data){const j=new Blob([JSON.stringify(g.data,null,2)],{type:"application/json"}),p=URL.createObjectURL(j),y=document.createElement("a");y.href=p,y.download=`lawvriksh-data-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(y),y.click(),document.body.removeChild(y),URL.revokeObjectURL(p)}}catch(g){console.error("Export failed:",g)}},R=async()=>{try{const u=await E.downloadData();if(u.success&&u.data){const g=new Blob([JSON.stringify(u.data,null,2)],{type:"application/json"}),j=URL.createObjectURL(g),p=document.createElement("a");p.href=j,p.download=`lawvriksh-complete-data-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(p),p.click(),document.body.removeChild(p),URL.revokeObjectURL(j)}}catch(u){console.error("Download failed:",u)}};return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-14 sm:h-16",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("img",{src:"/logo.png",alt:"LawVriksh",className:"h-6 sm:h-8 w-auto"}),e.jsx("h1",{className:"ml-2 sm:ml-4 text-lg sm:text-xl font-semibold text-gray-900 hidden sm:block",children:"Admin Dashboard"}),e.jsx("h1",{className:"ml-2 text-base font-semibold text-gray-900 sm:hidden",children:"Admin"})]}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-4",children:[e.jsx("button",{onClick:L,className:"px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-law-gold",disabled:r,children:r?"Refreshing...":"Refresh"}),e.jsx("button",{onClick:t,className:"px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500",children:"Logout"})]})]})})}),e.jsxs("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:e.jsx("div",{className:"p-5",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:e.jsx("span",{className:"text-white text-sm font-medium",children:"U"})})}),e.jsx("div",{className:"ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Users"}),e.jsx("dd",{className:"text-lg font-medium text-gray-900",children:l.totalUsers})]})})]})})}),e.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:e.jsx("div",{className:"p-5",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:e.jsx("span",{className:"text-white text-sm font-medium",children:"C"})})}),e.jsx("div",{className:"ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Creators"}),e.jsx("dd",{className:"text-lg font-medium text-gray-900",children:l.totalCreators})]})})]})})}),e.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:e.jsx("div",{className:"p-5",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center",children:e.jsx("span",{className:"text-white text-sm font-medium",children:"N"})})}),e.jsx("div",{className:"ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Not Interested"}),e.jsx("dd",{className:"text-lg font-medium text-gray-900",children:l.totalNotInterested})]})})]})})}),e.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:e.jsx("div",{className:"p-5",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center",children:e.jsx("span",{className:"text-white text-sm font-medium",children:"F"})})}),e.jsx("div",{className:"ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Feedback"}),e.jsx("dd",{className:"text-lg font-medium text-gray-900",children:l.totalFeedback})]})})]})})})]}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"border-b border-gray-200",children:e.jsxs("nav",{className:"-mb-px flex overflow-x-auto",children:[e.jsx("button",{onClick:()=>c("user-responses"),className:`py-2 px-3 sm:px-4 border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap ${a==="user-responses"?"border-law-gold text-law-gold":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"User Responses"}),e.jsx("button",{onClick:()=>c("feedback-responses"),className:`py-2 px-3 sm:px-4 border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap ${a==="feedback-responses"?"border-law-gold text-law-gold":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Feedback Responses"}),e.jsx("button",{onClick:()=>c("analytics"),className:`py-2 px-3 sm:px-4 border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap ${a==="analytics"?"border-law-gold text-law-gold":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Analytics"}),e.jsx("button",{onClick:()=>c("data-export"),className:`py-2 px-3 sm:px-4 border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap ${a==="data-export"?"border-law-gold text-law-gold":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Data Export"})]})}),e.jsxs("div",{className:"p-6",children:[a==="user-responses"&&e.jsxs("div",{children:[e.jsx("div",{className:"mb-4",children:e.jsxs("nav",{className:"flex space-x-4 sm:space-x-8 overflow-x-auto",children:[e.jsxs("button",{onClick:()=>i("user"),className:`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap ${s==="user"?"border-law-gold text-law-gold":"border-transparent text-gray-500 hover:text-gray-700"}`,children:["Users (",l.totalUsers,")"]}),e.jsxs("button",{onClick:()=>i("creator"),className:`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap ${s==="creator"?"border-law-gold text-law-gold":"border-transparent text-gray-500 hover:text-gray-700"}`,children:["Creators (",l.totalCreators,")"]}),e.jsxs("button",{onClick:()=>i("not-interested"),className:`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap ${s==="not-interested"?"border-law-gold text-law-gold":"border-transparent text-gray-500 hover:text-gray-700"}`,children:["Not Interested (",l.totalNotInterested,")"]})]})}),s==="user"&&e.jsx(N,{data:f,type:"users",isLoading:r}),s==="creator"&&e.jsx(N,{data:w,type:"creators",isLoading:r}),s==="not-interested"&&e.jsx(N,{data:v,type:"not-interested",isLoading:r})]}),a==="feedback-responses"&&e.jsx(N,{data:k,type:"feedback",isLoading:r}),a==="analytics"&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-6",children:"Analytics Dashboard"}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h4",{className:"text-md font-medium text-gray-800 mb-4",children:"User Analytics"}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-gray-600",children:"User analytics data would be displayed here, including:"}),e.jsxs("ul",{className:"list-disc list-inside text-sm text-gray-600 mt-2 space-y-1",children:[e.jsx("li",{children:"Registration trends over time"}),e.jsx("li",{children:"User vs Creator distribution"}),e.jsx("li",{children:"Geographic distribution"}),e.jsx("li",{children:"Profession breakdown"})]})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h4",{className:"text-md font-medium text-gray-800 mb-4",children:"Feedback Analytics"}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Feedback analytics data would be displayed here, including:"}),e.jsxs("ul",{className:"list-disc list-inside text-sm text-gray-600 mt-2 space-y-1",children:[e.jsx("li",{children:"Average ratings for UI/UX components"}),e.jsx("li",{children:"Common feedback themes"}),e.jsx("li",{children:"Response rates and completion statistics"}),e.jsx("li",{children:"Follow-up consent rates"})]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-md font-medium text-gray-800 mb-4",children:"Comprehensive Statistics"}),e.jsx("div",{className:"bg-gray-50 p-4 rounded-lg",children:e.jsx("p",{className:"text-sm text-gray-600",children:"Comprehensive statistics would be displayed here from the /api/data/stats endpoint."})})]})]}),a==="data-export"&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-6",children:"Data Export & Download"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-gray-50 p-6 rounded-lg",children:[e.jsx("h4",{className:"text-md font-medium text-gray-800 mb-4",children:"Export Data"}),e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Export all data in various formats for analysis or backup purposes."}),e.jsx("div",{className:"flex gap-4",children:e.jsx("button",{onClick:()=>T(),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"Export as JSON"})})]}),e.jsxs("div",{className:"bg-gray-50 p-6 rounded-lg",children:[e.jsx("h4",{className:"text-md font-medium text-gray-800 mb-4",children:"Download All Data"}),e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Download a comprehensive dataset including all users, creators, feedback, and analytics."}),e.jsx("button",{onClick:R,className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500",children:"Download All Data"})]}),e.jsxs("div",{className:"bg-gray-50 p-6 rounded-lg",children:[e.jsx("h4",{className:"text-md font-medium text-gray-800 mb-4",children:"Available API Endpoints"}),e.jsxs("div",{className:"text-sm text-gray-600 space-y-2",children:[e.jsxs("p",{children:[e.jsx("code",{className:"bg-gray-200 px-2 py-1 rounded",children:"POST /api/data/downloaddata"})," - Download all data"]}),e.jsxs("p",{children:[e.jsx("code",{className:"bg-gray-200 px-2 py-1 rounded",children:"GET /api/data/export/json"})," - Export as JSON file"]}),e.jsxs("p",{children:[e.jsx("code",{className:"bg-gray-200 px-2 py-1 rounded",children:"GET /api/data/stats"})," - Get comprehensive statistics"]}),e.jsxs("p",{children:[e.jsx("code",{className:"bg-gray-200 px-2 py-1 rounded",children:"GET /api/users/analytics"})," - User analytics"]}),e.jsxs("p",{children:[e.jsx("code",{className:"bg-gray-200 px-2 py-1 rounded",children:"GET /api/feedback/analytics"})," - Feedback analytics"]}),e.jsxs("p",{children:[e.jsx("code",{className:"bg-gray-200 px-2 py-1 rounded",children:"GET /api/feedback/summary"})," - Feedback summary"]})]})]})]})]})]})]})]})]})}const M=()=>[{title:"LawVriksh Admin - Dashboard"},{name:"description",content:"LawVriksh Admin Dashboard - Manage user registrations and feedback"},{name:"robots",content:"noindex, nofollow"}];function z(){U();const{isAuthenticated:n,logout:a,verifyToken:c}=S();b.useEffect(()=>{n&&c()},[n,c]);const s=()=>{a()};return e.jsx(e.Fragment,{children:n?e.jsx(_,{onLogout:s}):e.jsx(O,{})})}export{z as default,M as meta};
