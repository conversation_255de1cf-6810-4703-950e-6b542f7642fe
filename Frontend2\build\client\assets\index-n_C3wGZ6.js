function Xr(e,t){for(var r=0;r<t.length;r++){const n=t[r];if(typeof n!="string"&&!Array.isArray(n)){for(const a in n)if(a!=="default"&&!(a in e)){const l=Object.getOwnPropertyDescriptor(n,a);l&&Object.defineProperty(e,a,l.get?l:{enumerable:!0,get:()=>n[a]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}function Qr(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Et={exports:{}},Xe={},Rt={exports:{}},F={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zt;function Zr(){if(Zt)return F;Zt=1;var e=Symbol.for("react.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),i=Symbol.for("react.context"),d=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),p=Symbol.iterator;function b(c){return c===null||typeof c!="object"?null:(c=p&&c[p]||c["@@iterator"],typeof c=="function"?c:null)}var E={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},M=Object.assign,L={};function D(c,g,A){this.props=c,this.context=g,this.refs=L,this.updater=A||E}D.prototype.isReactComponent={},D.prototype.setState=function(c,g){if(typeof c!="object"&&typeof c!="function"&&c!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,c,g,"setState")},D.prototype.forceUpdate=function(c){this.updater.enqueueForceUpdate(this,c,"forceUpdate")};function $(){}$.prototype=D.prototype;function O(c,g,A){this.props=c,this.context=g,this.refs=L,this.updater=A||E}var te=O.prototype=new $;te.constructor=O,M(te,D.prototype),te.isPureReactComponent=!0;var se=Array.isArray,ie=Object.prototype.hasOwnProperty,Ee={current:null},h={key:!0,ref:!0,__self:!0,__source:!0};function K(c,g,A){var B,U={},V=null,J=null;if(g!=null)for(B in g.ref!==void 0&&(J=g.ref),g.key!==void 0&&(V=""+g.key),g)ie.call(g,B)&&!h.hasOwnProperty(B)&&(U[B]=g[B]);var Y=arguments.length-2;if(Y===1)U.children=A;else if(1<Y){for(var Q=Array(Y),pe=0;pe<Y;pe++)Q[pe]=arguments[pe+2];U.children=Q}if(c&&c.defaultProps)for(B in Y=c.defaultProps,Y)U[B]===void 0&&(U[B]=Y[B]);return{$$typeof:e,type:c,key:V,ref:J,props:U,_owner:Ee.current}}function oe(c,g){return{$$typeof:e,type:c.type,key:g,ref:c.ref,props:c.props,_owner:c._owner}}function W(c){return typeof c=="object"&&c!==null&&c.$$typeof===e}function ue(c){var g={"=":"=0",":":"=2"};return"$"+c.replace(/[=:]/g,function(A){return g[A]})}var ce=/\/+/g;function de(c,g){return typeof c=="object"&&c!==null&&c.key!=null?ue(""+c.key):g.toString(36)}function ge(c,g,A,B,U){var V=typeof c;(V==="undefined"||V==="boolean")&&(c=null);var J=!1;if(c===null)J=!0;else switch(V){case"string":case"number":J=!0;break;case"object":switch(c.$$typeof){case e:case t:J=!0}}if(J)return J=c,U=U(J),c=B===""?"."+de(J,0):B,se(U)?(A="",c!=null&&(A=c.replace(ce,"$&/")+"/"),ge(U,g,A,"",function(pe){return pe})):U!=null&&(W(U)&&(U=oe(U,A+(!U.key||J&&J.key===U.key?"":(""+U.key).replace(ce,"$&/")+"/")+c)),g.push(U)),1;if(J=0,B=B===""?".":B+":",se(c))for(var Y=0;Y<c.length;Y++){V=c[Y];var Q=B+de(V,Y);J+=ge(V,g,A,Q,U)}else if(Q=b(c),typeof Q=="function")for(c=Q.call(c),Y=0;!(V=c.next()).done;)V=V.value,Q=B+de(V,Y++),J+=ge(V,g,A,Q,U);else if(V==="object")throw g=String(c),Error("Objects are not valid as a React child (found: "+(g==="[object Object]"?"object with keys {"+Object.keys(c).join(", ")+"}":g)+"). If you meant to render a collection of children, use an array instead.");return J}function ve(c,g,A){if(c==null)return c;var B=[],U=0;return ge(c,B,"","",function(V){return g.call(A,V,U++)}),B}function Ne(c){if(c._status===-1){var g=c._result;g=g(),g.then(function(A){(c._status===0||c._status===-1)&&(c._status=1,c._result=A)},function(A){(c._status===0||c._status===-1)&&(c._status=2,c._result=A)}),c._status===-1&&(c._status=0,c._result=g)}if(c._status===1)return c._result.default;throw c._result}var re={current:null},X={transition:null},ke={ReactCurrentDispatcher:re,ReactCurrentBatchConfig:X,ReactCurrentOwner:Ee};function Se(){throw Error("act(...) is not supported in production builds of React.")}return F.Children={map:ve,forEach:function(c,g,A){ve(c,function(){g.apply(this,arguments)},A)},count:function(c){var g=0;return ve(c,function(){g++}),g},toArray:function(c){return ve(c,function(g){return g})||[]},only:function(c){if(!W(c))throw Error("React.Children.only expected to receive a single React element child.");return c}},F.Component=D,F.Fragment=r,F.Profiler=a,F.PureComponent=O,F.StrictMode=n,F.Suspense=u,F.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ke,F.act=Se,F.cloneElement=function(c,g,A){if(c==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+c+".");var B=M({},c.props),U=c.key,V=c.ref,J=c._owner;if(g!=null){if(g.ref!==void 0&&(V=g.ref,J=Ee.current),g.key!==void 0&&(U=""+g.key),c.type&&c.type.defaultProps)var Y=c.type.defaultProps;for(Q in g)ie.call(g,Q)&&!h.hasOwnProperty(Q)&&(B[Q]=g[Q]===void 0&&Y!==void 0?Y[Q]:g[Q])}var Q=arguments.length-2;if(Q===1)B.children=A;else if(1<Q){Y=Array(Q);for(var pe=0;pe<Q;pe++)Y[pe]=arguments[pe+2];B.children=Y}return{$$typeof:e,type:c.type,key:U,ref:V,props:B,_owner:J}},F.createContext=function(c){return c={$$typeof:i,_currentValue:c,_currentValue2:c,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},c.Provider={$$typeof:l,_context:c},c.Consumer=c},F.createElement=K,F.createFactory=function(c){var g=K.bind(null,c);return g.type=c,g},F.createRef=function(){return{current:null}},F.forwardRef=function(c){return{$$typeof:d,render:c}},F.isValidElement=W,F.lazy=function(c){return{$$typeof:v,_payload:{_status:-1,_result:c},_init:Ne}},F.memo=function(c,g){return{$$typeof:m,type:c,compare:g===void 0?null:g}},F.startTransition=function(c){var g=X.transition;X.transition={};try{c()}finally{X.transition=g}},F.unstable_act=Se,F.useCallback=function(c,g){return re.current.useCallback(c,g)},F.useContext=function(c){return re.current.useContext(c)},F.useDebugValue=function(){},F.useDeferredValue=function(c){return re.current.useDeferredValue(c)},F.useEffect=function(c,g){return re.current.useEffect(c,g)},F.useId=function(){return re.current.useId()},F.useImperativeHandle=function(c,g,A){return re.current.useImperativeHandle(c,g,A)},F.useInsertionEffect=function(c,g){return re.current.useInsertionEffect(c,g)},F.useLayoutEffect=function(c,g){return re.current.useLayoutEffect(c,g)},F.useMemo=function(c,g){return re.current.useMemo(c,g)},F.useReducer=function(c,g,A){return re.current.useReducer(c,g,A)},F.useRef=function(c){return re.current.useRef(c)},F.useState=function(c){return re.current.useState(c)},F.useSyncExternalStore=function(c,g,A){return re.current.useSyncExternalStore(c,g,A)},F.useTransition=function(){return re.current.useTransition()},F.version="18.3.1",F}var er;function Er(){return er||(er=1,Rt.exports=Zr()),Rt.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var tr;function en(){if(tr)return Xe;tr=1;var e=Er(),t=Symbol.for("react.element"),r=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,a=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function i(d,u,m){var v,p={},b=null,E=null;m!==void 0&&(b=""+m),u.key!==void 0&&(b=""+u.key),u.ref!==void 0&&(E=u.ref);for(v in u)n.call(u,v)&&!l.hasOwnProperty(v)&&(p[v]=u[v]);if(d&&d.defaultProps)for(v in u=d.defaultProps,u)p[v]===void 0&&(p[v]=u[v]);return{$$typeof:t,type:d,key:b,ref:E,props:p,_owner:a.current}}return Xe.Fragment=r,Xe.jsx=i,Xe.jsxs=i,Xe}var rr;function tn(){return rr||(rr=1,Et.exports=en()),Et.exports}var ba=tn(),R=Er();const rn=Qr(R),wa=Xr({__proto__:null,default:rn},[R]);/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function G(){return G=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},G.apply(this,arguments)}var ae;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(ae||(ae={}));const nr="popstate";function Ea(e){e===void 0&&(e={});function t(n,a){let{pathname:l,search:i,hash:d}=n.location;return tt("",{pathname:l,search:i,hash:d},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(n,a){return typeof a=="string"?a:nt(a)}return an(t,r,null,e)}function I(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Ve(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function nn(){return Math.random().toString(36).substr(2,8)}function ar(e,t){return{usr:e.state,key:e.key,idx:t}}function tt(e,t,r,n){return r===void 0&&(r=null),G({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Le(t):t,{state:r,key:t&&t.key||n||nn()})}function nt(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function Le(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function an(e,t,r,n){n===void 0&&(n={});let{window:a=document.defaultView,v5Compat:l=!1}=n,i=a.history,d=ae.Pop,u=null,m=v();m==null&&(m=0,i.replaceState(G({},i.state,{idx:m}),""));function v(){return(i.state||{idx:null}).idx}function p(){d=ae.Pop;let D=v(),$=D==null?null:D-m;m=D,u&&u({action:d,location:L.location,delta:$})}function b(D,$){d=ae.Push;let O=tt(L.location,D,$);m=v()+1;let te=ar(O,m),se=L.createHref(O);try{i.pushState(te,"",se)}catch(ie){if(ie instanceof DOMException&&ie.name==="DataCloneError")throw ie;a.location.assign(se)}l&&u&&u({action:d,location:L.location,delta:1})}function E(D,$){d=ae.Replace;let O=tt(L.location,D,$);m=v();let te=ar(O,m),se=L.createHref(O);i.replaceState(te,"",se),l&&u&&u({action:d,location:L.location,delta:0})}function M(D){let $=a.location.origin!=="null"?a.location.origin:a.location.href,O=typeof D=="string"?D:nt(D);return O=O.replace(/ $/,"%20"),I($,"No window.location.(origin|href) available to create URL for href: "+O),new URL(O,$)}let L={get action(){return d},get location(){return e(a,i)},listen(D){if(u)throw new Error("A history only accepts one active listener");return a.addEventListener(nr,p),u=D,()=>{a.removeEventListener(nr,p),u=null}},createHref(D){return t(a,D)},createURL:M,encodeLocation(D){let $=M(D);return{pathname:$.pathname,search:$.search,hash:$.hash}},push:b,replace:E,go(D){return i.go(D)}};return L}var H;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(H||(H={}));const on=new Set(["lazy","caseSensitive","path","id","index","children"]);function ln(e){return e.index===!0}function pt(e,t,r,n){return r===void 0&&(r=[]),n===void 0&&(n={}),e.map((a,l)=>{let i=[...r,String(l)],d=typeof a.id=="string"?a.id:i.join("-");if(I(a.index!==!0||!a.children,"Cannot specify children on an index route"),I(!n[d],'Found a route id collision on id "'+d+`".  Route id's must be globally unique within Data Router usages`),ln(a)){let u=G({},a,t(a),{id:d});return n[d]=u,u}else{let u=G({},a,t(a),{id:d,children:void 0});return n[d]=u,a.children&&(u.children=pt(a.children,t,i,n)),u}})}function Fe(e,t,r){return r===void 0&&(r="/"),ht(e,t,r,!1)}function ht(e,t,r,n){let a=typeof t=="string"?Le(t):t,l=at(a.pathname||"/",r);if(l==null)return null;let i=xr(e);sn(i);let d=null;for(let u=0;d==null&&u<i.length;++u){let m=bn(l);d=yn(i[u],m,n)}return d}function Rr(e,t){let{route:r,pathname:n,params:a}=e;return{id:r.id,pathname:n,params:a,data:t[r.id],handle:r.handle}}function xr(e,t,r,n){t===void 0&&(t=[]),r===void 0&&(r=[]),n===void 0&&(n="");let a=(l,i,d)=>{let u={relativePath:d===void 0?l.path||"":d,caseSensitive:l.caseSensitive===!0,childrenIndex:i,route:l};u.relativePath.startsWith("/")&&(I(u.relativePath.startsWith(n),'Absolute route path "'+u.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),u.relativePath=u.relativePath.slice(n.length));let m=Pe([n,u.relativePath]),v=r.concat(u);l.children&&l.children.length>0&&(I(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+m+'".')),xr(l.children,t,v,m)),!(l.path==null&&!l.index)&&t.push({path:m,score:mn(m,l.index),routesMeta:v})};return e.forEach((l,i)=>{var d;if(l.path===""||!((d=l.path)!=null&&d.includes("?")))a(l,i);else for(let u of Pr(l.path))a(l,i,u)}),t}function Pr(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,a=r.endsWith("?"),l=r.replace(/\?$/,"");if(n.length===0)return a?[l,""]:[l];let i=Pr(n.join("/")),d=[];return d.push(...i.map(u=>u===""?l:[l,u].join("/"))),a&&d.push(...i),d.map(u=>e.startsWith("/")&&u===""?"/":u)}function sn(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:vn(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const un=/^:[\w-]+$/,cn=3,dn=2,fn=1,hn=10,pn=-2,or=e=>e==="*";function mn(e,t){let r=e.split("/"),n=r.length;return r.some(or)&&(n+=pn),t&&(n+=dn),r.filter(a=>!or(a)).reduce((a,l)=>a+(un.test(l)?cn:l===""?fn:hn),n)}function vn(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function yn(e,t,r){r===void 0&&(r=!1);let{routesMeta:n}=e,a={},l="/",i=[];for(let d=0;d<n.length;++d){let u=n[d],m=d===n.length-1,v=l==="/"?t:t.slice(l.length)||"/",p=ir({path:u.relativePath,caseSensitive:u.caseSensitive,end:m},v),b=u.route;if(!p&&m&&r&&!n[n.length-1].route.index&&(p=ir({path:u.relativePath,caseSensitive:u.caseSensitive,end:!1},v)),!p)return null;Object.assign(a,p.params),i.push({params:a,pathname:Pe([l,p.pathname]),pathnameBase:Rn(Pe([l,p.pathnameBase])),route:b}),p.pathnameBase!=="/"&&(l=Pe([l,p.pathnameBase]))}return i}function ir(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=gn(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),d=a.slice(1);return{params:n.reduce((m,v,p)=>{let{paramName:b,isOptional:E}=v;if(b==="*"){let L=d[p]||"";i=l.slice(0,l.length-L.length).replace(/(.)\/+$/,"$1")}const M=d[p];return E&&!M?m[b]=void 0:m[b]=(M||"").replace(/%2F/g,"/"),m},{}),pathname:l,pathnameBase:i,pattern:e}}function gn(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),Ve(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,d,u)=>(n.push({paramName:d,isOptional:u!=null}),u?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function bn(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Ve(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function at(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function wn(e,t){t===void 0&&(t="/");let{pathname:r,search:n="",hash:a=""}=typeof e=="string"?Le(e):e;return{pathname:r?r.startsWith("/")?r:En(r,t):t,search:xn(n),hash:Pn(a)}}function En(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function xt(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Sr(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function jt(e,t){let r=Sr(e);return t?r.map((n,a)=>a===r.length-1?n.pathname:n.pathnameBase):r.map(n=>n.pathnameBase)}function Ot(e,t,r,n){n===void 0&&(n=!1);let a;typeof e=="string"?a=Le(e):(a=G({},e),I(!a.pathname||!a.pathname.includes("?"),xt("?","pathname","search",a)),I(!a.pathname||!a.pathname.includes("#"),xt("#","pathname","hash",a)),I(!a.search||!a.search.includes("#"),xt("#","search","hash",a)));let l=e===""||a.pathname==="",i=l?"/":a.pathname,d;if(i==null)d=r;else{let p=t.length-1;if(!n&&i.startsWith("..")){let b=i.split("/");for(;b[0]==="..";)b.shift(),p-=1;a.pathname=b.join("/")}d=p>=0?t[p]:"/"}let u=wn(a,d),m=i&&i!=="/"&&i.endsWith("/"),v=(l||i===".")&&r.endsWith("/");return!u.pathname.endsWith("/")&&(m||v)&&(u.pathname+="/"),u}const Pe=e=>e.join("/").replace(/\/\/+/g,"/"),Rn=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),xn=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Pn=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;class Sn{constructor(t,r){this.type="DataWithResponseInit",this.data=t,this.init=r||null}}function Ra(e,t){return new Sn(e,typeof t=="number"?{status:t}:t)}class Dt extends Error{}class xa{constructor(t,r){this.pendingKeysSet=new Set,this.subscribers=new Set,this.deferredKeys=[],I(t&&typeof t=="object"&&!Array.isArray(t),"defer() only accepts plain objects");let n;this.abortPromise=new Promise((l,i)=>n=i),this.controller=new AbortController;let a=()=>n(new Dt("Deferred data aborted"));this.unlistenAbortSignal=()=>this.controller.signal.removeEventListener("abort",a),this.controller.signal.addEventListener("abort",a),this.data=Object.entries(t).reduce((l,i)=>{let[d,u]=i;return Object.assign(l,{[d]:this.trackPromise(d,u)})},{}),this.done&&this.unlistenAbortSignal(),this.init=r}trackPromise(t,r){if(!(r instanceof Promise))return r;this.deferredKeys.push(t),this.pendingKeysSet.add(t);let n=Promise.race([r,this.abortPromise]).then(a=>this.onSettle(n,t,void 0,a),a=>this.onSettle(n,t,a));return n.catch(()=>{}),Object.defineProperty(n,"_tracked",{get:()=>!0}),n}onSettle(t,r,n,a){if(this.controller.signal.aborted&&n instanceof Dt)return this.unlistenAbortSignal(),Object.defineProperty(t,"_error",{get:()=>n}),Promise.reject(n);if(this.pendingKeysSet.delete(r),this.done&&this.unlistenAbortSignal(),n===void 0&&a===void 0){let l=new Error('Deferred data for key "'+r+'" resolved/rejected with `undefined`, you must resolve/reject with a value or `null`.');return Object.defineProperty(t,"_error",{get:()=>l}),this.emit(!1,r),Promise.reject(l)}return a===void 0?(Object.defineProperty(t,"_error",{get:()=>n}),this.emit(!1,r),Promise.reject(n)):(Object.defineProperty(t,"_data",{get:()=>a}),this.emit(!1,r),a)}emit(t,r){this.subscribers.forEach(n=>n(t,r))}subscribe(t){return this.subscribers.add(t),()=>this.subscribers.delete(t)}cancel(){this.controller.abort(),this.pendingKeysSet.forEach((t,r)=>this.pendingKeysSet.delete(r)),this.emit(!0)}async resolveData(t){let r=!1;if(!this.done){let n=()=>this.cancel();t.addEventListener("abort",n),r=await new Promise(a=>{this.subscribe(l=>{t.removeEventListener("abort",n),(l||this.done)&&a(l)})})}return r}get done(){return this.pendingKeysSet.size===0}get unwrappedData(){return I(this.data!==null&&this.done,"Can only unwrap data on initialized and settled deferreds"),Object.entries(this.data).reduce((t,r)=>{let[n,a]=r;return Object.assign(t,{[n]:_n(a)})},{})}get pendingKeys(){return Array.from(this.pendingKeysSet)}}function Dn(e){return e instanceof Promise&&e._tracked===!0}function _n(e){if(!Dn(e))return e;if(e._error)throw e._error;return e._data}const Pa=function(t,r){r===void 0&&(r=302);let n=r;typeof n=="number"?n={status:n}:typeof n.status>"u"&&(n.status=302);let a=new Headers(n.headers);return a.set("Location",t),new Response(null,G({},n,{headers:a}))};class _t{constructor(t,r,n,a){a===void 0&&(a=!1),this.status=t,this.statusText=r||"",this.internal=a,n instanceof Error?(this.data=n.toString(),this.error=n):this.data=n}}function rt(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Dr=["post","put","patch","delete"],Cn=new Set(Dr),Mn=["get",...Dr],Ln=new Set(Mn),jn=new Set([301,302,303,307,308]),On=new Set([307,308]),Pt={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Tn={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Qe={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},Tt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Un=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),_r="remix-router-transitions";function Sa(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u",n=!r;I(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let a;if(e.mapRouteProperties)a=e.mapRouteProperties;else if(e.detectErrorBoundary){let o=e.detectErrorBoundary;a=s=>({hasErrorBoundary:o(s)})}else a=Un;let l={},i=pt(e.routes,a,void 0,l),d,u=e.basename||"/",m=e.dataStrategy||Nn,v=e.patchRoutesOnNavigation,p=G({v7_fetcherPersist:!1,v7_normalizeFormMethod:!1,v7_partialHydration:!1,v7_prependBasename:!1,v7_relativeSplatPath:!1,v7_skipActionErrorRevalidation:!1},e.future),b=null,E=new Set,M=null,L=null,D=null,$=e.hydrationData!=null,O=Fe(i,e.history.location,u),te=!1,se=null;if(O==null&&!v){let o=he(404,{pathname:e.history.location.pathname}),{matches:s,route:f}=yr(i);O=s,se={[f.id]:o}}O&&!e.hydrationData&&st(O,i,e.history.location.pathname).active&&(O=null);let ie;if(O)if(O.some(o=>o.route.lazy))ie=!1;else if(!O.some(o=>o.route.loader))ie=!0;else if(p.v7_partialHydration){let o=e.hydrationData?e.hydrationData.loaderData:null,s=e.hydrationData?e.hydrationData.errors:null;if(s){let f=O.findIndex(y=>s[y.route.id]!==void 0);ie=O.slice(0,f+1).every(y=>!Mt(y.route,o,s))}else ie=O.every(f=>!Mt(f.route,o,s))}else ie=e.hydrationData!=null;else if(ie=!1,O=[],p.v7_partialHydration){let o=st(null,i,e.history.location.pathname);o.active&&o.matches&&(te=!0,O=o.matches)}let Ee,h={historyAction:e.history.action,location:e.history.location,matches:O,initialized:ie,navigation:Pt,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||se,fetchers:new Map,blockers:new Map},K=ae.Pop,oe=!1,W,ue=!1,ce=new Map,de=null,ge=!1,ve=!1,Ne=[],re=new Set,X=new Map,ke=0,Se=-1,c=new Map,g=new Set,A=new Map,B=new Map,U=new Set,V=new Map,J=new Map,Y;function Q(){if(b=e.history.listen(o=>{let{action:s,location:f,delta:y}=o;if(Y){Y(),Y=void 0;return}Ve(J.size===0||y!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let w=Yt({currentLocation:h.location,nextLocation:f,historyAction:s});if(w&&y!=null){let C=new Promise(j=>{Y=j});e.history.go(y*-1),lt(w,{state:"blocked",location:f,proceed(){lt(w,{state:"proceeding",proceed:void 0,reset:void 0,location:f}),C.then(()=>e.history.go(y))},reset(){let j=new Map(h.blockers);j.set(w,Qe),fe({blockers:j})}});return}return Oe(s,f)}),r){Qn(t,ce);let o=()=>Zn(t,ce);t.addEventListener("pagehide",o),de=()=>t.removeEventListener("pagehide",o)}return h.initialized||Oe(ae.Pop,h.location,{initialHydration:!0}),Ee}function pe(){b&&b(),de&&de(),E.clear(),W&&W.abort(),h.fetchers.forEach((o,s)=>it(s)),h.blockers.forEach((o,s)=>qt(s))}function Ir(o){return E.add(o),()=>E.delete(o)}function fe(o,s){s===void 0&&(s={}),h=G({},h,o);let f=[],y=[];p.v7_fetcherPersist&&h.fetchers.forEach((w,C)=>{w.state==="idle"&&(U.has(C)?y.push(C):f.push(C))}),U.forEach(w=>{!h.fetchers.has(w)&&!X.has(w)&&y.push(w)}),[...E].forEach(w=>w(h,{deletedFetchers:y,viewTransitionOpts:s.viewTransitionOpts,flushSync:s.flushSync===!0})),p.v7_fetcherPersist?(f.forEach(w=>h.fetchers.delete(w)),y.forEach(w=>it(w))):y.forEach(w=>U.delete(w))}function Be(o,s,f){var y,w;let{flushSync:C}=f===void 0?{}:f,j=h.actionData!=null&&h.navigation.formMethod!=null&&be(h.navigation.formMethod)&&h.navigation.state==="loading"&&((y=o.state)==null?void 0:y._isRedirect)!==!0,P;s.actionData?Object.keys(s.actionData).length>0?P=s.actionData:P=null:j?P=h.actionData:P=null;let S=s.loaderData?mr(h.loaderData,s.loaderData,s.matches||[],s.errors):h.loaderData,x=h.blockers;x.size>0&&(x=new Map(x),x.forEach((N,le)=>x.set(le,Qe)));let _=oe===!0||h.navigation.formMethod!=null&&be(h.navigation.formMethod)&&((w=o.state)==null?void 0:w._isRedirect)!==!0;d&&(i=d,d=void 0),ge||K===ae.Pop||(K===ae.Push?e.history.push(o,o.state):K===ae.Replace&&e.history.replace(o,o.state));let T;if(K===ae.Pop){let N=ce.get(h.location.pathname);N&&N.has(o.pathname)?T={currentLocation:h.location,nextLocation:o}:ce.has(o.pathname)&&(T={currentLocation:o,nextLocation:h.location})}else if(ue){let N=ce.get(h.location.pathname);N?N.add(o.pathname):(N=new Set([o.pathname]),ce.set(h.location.pathname,N)),T={currentLocation:h.location,nextLocation:o}}fe(G({},s,{actionData:P,loaderData:S,historyAction:K,location:o,initialized:!0,navigation:Pt,revalidation:"idle",restoreScrollPosition:Xt(o,s.matches||h.matches),preventScrollReset:_,blockers:x}),{viewTransitionOpts:T,flushSync:C===!0}),K=ae.Pop,oe=!1,ue=!1,ge=!1,ve=!1,Ne=[]}async function zt(o,s){if(typeof o=="number"){e.history.go(o);return}let f=Ct(h.location,h.matches,u,p.v7_prependBasename,o,p.v7_relativeSplatPath,s==null?void 0:s.fromRouteId,s==null?void 0:s.relative),{path:y,submission:w,error:C}=lr(p.v7_normalizeFormMethod,!1,f,s),j=h.location,P=tt(h.location,y,s&&s.state);P=G({},P,e.history.encodeLocation(P));let S=s&&s.replace!=null?s.replace:void 0,x=ae.Push;S===!0?x=ae.Replace:S===!1||w!=null&&be(w.formMethod)&&w.formAction===h.location.pathname+h.location.search&&(x=ae.Replace);let _=s&&"preventScrollReset"in s?s.preventScrollReset===!0:void 0,T=(s&&s.flushSync)===!0,N=Yt({currentLocation:j,nextLocation:P,historyAction:x});if(N){lt(N,{state:"blocked",location:P,proceed(){lt(N,{state:"proceeding",proceed:void 0,reset:void 0,location:P}),zt(o,s)},reset(){let le=new Map(h.blockers);le.set(N,Qe),fe({blockers:le})}});return}return await Oe(x,P,{submission:w,pendingError:C,preventScrollReset:_,replace:s&&s.replace,enableViewTransition:s&&s.viewTransition,flushSync:T})}function Ar(){if(yt(),fe({revalidation:"loading"}),h.navigation.state!=="submitting"){if(h.navigation.state==="idle"){Oe(h.historyAction,h.location,{startUninterruptedRevalidation:!0});return}Oe(K||h.historyAction,h.navigation.location,{overrideNavigation:h.navigation,enableViewTransition:ue===!0})}}async function Oe(o,s,f){W&&W.abort(),W=null,K=o,ge=(f&&f.startUninterruptedRevalidation)===!0,Jr(h.location,h.matches),oe=(f&&f.preventScrollReset)===!0,ue=(f&&f.enableViewTransition)===!0;let y=d||i,w=f&&f.overrideNavigation,C=f!=null&&f.initialHydration&&h.matches&&h.matches.length>0&&!te?h.matches:Fe(y,s,u),j=(f&&f.flushSync)===!0;if(C&&h.initialized&&!ve&&Wn(h.location,s)&&!(f&&f.submission&&be(f.submission.formMethod))){Be(s,{matches:C},{flushSync:j});return}let P=st(C,y,s.pathname);if(P.active&&P.matches&&(C=P.matches),!C){let{error:q,notFoundMatches:z,route:Z}=gt(s.pathname);Be(s,{matches:z,loaderData:{},errors:{[Z.id]:q}},{flushSync:j});return}W=new AbortController;let S=We(e.history,s,W.signal,f&&f.submission),x;if(f&&f.pendingError)x=[Ie(C).route.id,{type:H.error,error:f.pendingError}];else if(f&&f.submission&&be(f.submission.formMethod)){let q=await Nr(S,s,f.submission,C,P.active,{replace:f.replace,flushSync:j});if(q.shortCircuited)return;if(q.pendingActionResult){let[z,Z]=q.pendingActionResult;if(me(Z)&&rt(Z.error)&&Z.error.status===404){W=null,Be(s,{matches:q.matches,loaderData:{},errors:{[z]:Z.error}});return}}C=q.matches||C,x=q.pendingActionResult,w=St(s,f.submission),j=!1,P.active=!1,S=We(e.history,S.url,S.signal)}let{shortCircuited:_,matches:T,loaderData:N,errors:le}=await kr(S,s,C,P.active,w,f&&f.submission,f&&f.fetcherSubmission,f&&f.replace,f&&f.initialHydration===!0,j,x);_||(W=null,Be(s,G({matches:T||C},vr(x),{loaderData:N,errors:le})))}async function Nr(o,s,f,y,w,C){C===void 0&&(C={}),yt();let j=Gn(s,f);if(fe({navigation:j},{flushSync:C.flushSync===!0}),w){let x=await ut(y,s.pathname,o.signal);if(x.type==="aborted")return{shortCircuited:!0};if(x.type==="error"){let _=Ie(x.partialMatches).route.id;return{matches:x.partialMatches,pendingActionResult:[_,{type:H.error,error:x.error}]}}else if(x.matches)y=x.matches;else{let{notFoundMatches:_,error:T,route:N}=gt(s.pathname);return{matches:_,pendingActionResult:[N.id,{type:H.error,error:T}]}}}let P,S=et(y,s);if(!S.route.action&&!S.route.lazy)P={type:H.error,error:he(405,{method:o.method,pathname:s.pathname,routeId:S.route.id})};else if(P=(await Je("action",h,o,[S],y,null))[S.route.id],o.signal.aborted)return{shortCircuited:!0};if(Ae(P)){let x;return C&&C.replace!=null?x=C.replace:x=fr(P.response.headers.get("Location"),new URL(o.url),u)===h.location.pathname+h.location.search,await Te(o,P,!0,{submission:f,replace:x}),{shortCircuited:!0}}if(Me(P))throw he(400,{type:"defer-action"});if(me(P)){let x=Ie(y,S.route.id);return(C&&C.replace)!==!0&&(K=ae.Push),{matches:y,pendingActionResult:[x.route.id,P]}}return{matches:y,pendingActionResult:[S.route.id,P]}}async function kr(o,s,f,y,w,C,j,P,S,x,_){let T=w||St(s,C),N=C||j||br(T),le=!ge&&(!p.v7_partialHydration||!S);if(y){if(le){let ee=$t(_);fe(G({navigation:T},ee!==void 0?{actionData:ee}:{}),{flushSync:x})}let k=await ut(f,s.pathname,o.signal);if(k.type==="aborted")return{shortCircuited:!0};if(k.type==="error"){let ee=Ie(k.partialMatches).route.id;return{matches:k.partialMatches,loaderData:{},errors:{[ee]:k.error}}}else if(k.matches)f=k.matches;else{let{error:ee,notFoundMatches:$e,route:Ge}=gt(s.pathname);return{matches:$e,loaderData:{},errors:{[Ge.id]:ee}}}}let q=d||i,[z,Z]=ur(e.history,h,f,N,s,p.v7_partialHydration&&S===!0,p.v7_skipActionErrorRevalidation,ve,Ne,re,U,A,g,q,u,_);if(bt(k=>!(f&&f.some(ee=>ee.route.id===k))||z&&z.some(ee=>ee.route.id===k)),Se=++ke,z.length===0&&Z.length===0){let k=Kt();return Be(s,G({matches:f,loaderData:{},errors:_&&me(_[1])?{[_[0]]:_[1].error}:null},vr(_),k?{fetchers:new Map(h.fetchers)}:{}),{flushSync:x}),{shortCircuited:!0}}if(le){let k={};if(!y){k.navigation=T;let ee=$t(_);ee!==void 0&&(k.actionData=ee)}Z.length>0&&(k.fetchers=Br(Z)),fe(k,{flushSync:x})}Z.forEach(k=>{_e(k.key),k.controller&&X.set(k.key,k.controller)});let ze=()=>Z.forEach(k=>_e(k.key));W&&W.signal.addEventListener("abort",ze);let{loaderResults:qe,fetcherResults:xe}=await Ht(h,f,z,Z,o);if(o.signal.aborted)return{shortCircuited:!0};W&&W.signal.removeEventListener("abort",ze),Z.forEach(k=>X.delete(k.key));let we=ft(qe);if(we)return await Te(o,we.result,!0,{replace:P}),{shortCircuited:!0};if(we=ft(xe),we)return g.add(we.key),await Te(o,we.result,!0,{replace:P}),{shortCircuited:!0};let{loaderData:wt,errors:Ye}=pr(h,f,qe,_,Z,xe,V);V.forEach((k,ee)=>{k.subscribe($e=>{($e||k.done)&&V.delete(ee)})}),p.v7_partialHydration&&S&&h.errors&&(Ye=G({},h.errors,Ye));let Ue=Kt(),ct=Jt(Se),dt=Ue||ct||Z.length>0;return G({matches:f,loaderData:wt,errors:Ye},dt?{fetchers:new Map(h.fetchers)}:{})}function $t(o){if(o&&!me(o[1]))return{[o[0]]:o[1].data};if(h.actionData)return Object.keys(h.actionData).length===0?null:h.actionData}function Br(o){return o.forEach(s=>{let f=h.fetchers.get(s.key),y=Ze(void 0,f?f.data:void 0);h.fetchers.set(s.key,y)}),new Map(h.fetchers)}function zr(o,s,f,y){if(n)throw new Error("router.fetch() was called during the server render, but it shouldn't be. You are likely calling a useFetcher() method in the body of your component. Try moving it to a useEffect or a callback.");_e(o);let w=(y&&y.flushSync)===!0,C=d||i,j=Ct(h.location,h.matches,u,p.v7_prependBasename,f,p.v7_relativeSplatPath,s,y==null?void 0:y.relative),P=Fe(C,j,u),S=st(P,C,j);if(S.active&&S.matches&&(P=S.matches),!P){Re(o,s,he(404,{pathname:j}),{flushSync:w});return}let{path:x,submission:_,error:T}=lr(p.v7_normalizeFormMethod,!0,j,y);if(T){Re(o,s,T,{flushSync:w});return}let N=et(P,x),le=(y&&y.preventScrollReset)===!0;if(_&&be(_.formMethod)){$r(o,s,x,N,P,S.active,w,le,_);return}A.set(o,{routeId:s,path:x}),Hr(o,s,x,N,P,S.active,w,le,_)}async function $r(o,s,f,y,w,C,j,P,S){yt(),A.delete(o);function x(ne){if(!ne.route.action&&!ne.route.lazy){let He=he(405,{method:S.formMethod,pathname:f,routeId:s});return Re(o,s,He,{flushSync:j}),!0}return!1}if(!C&&x(y))return;let _=h.fetchers.get(o);De(o,Xn(S,_),{flushSync:j});let T=new AbortController,N=We(e.history,f,T.signal,S);if(C){let ne=await ut(w,new URL(N.url).pathname,N.signal,o);if(ne.type==="aborted")return;if(ne.type==="error"){Re(o,s,ne.error,{flushSync:j});return}else if(ne.matches){if(w=ne.matches,y=et(w,f),x(y))return}else{Re(o,s,he(404,{pathname:f}),{flushSync:j});return}}X.set(o,T);let le=ke,z=(await Je("action",h,N,[y],w,o))[y.route.id];if(N.signal.aborted){X.get(o)===T&&X.delete(o);return}if(p.v7_fetcherPersist&&U.has(o)){if(Ae(z)||me(z)){De(o,Ce(void 0));return}}else{if(Ae(z))if(X.delete(o),Se>le){De(o,Ce(void 0));return}else return g.add(o),De(o,Ze(S)),Te(N,z,!1,{fetcherSubmission:S,preventScrollReset:P});if(me(z)){Re(o,s,z.error);return}}if(Me(z))throw he(400,{type:"defer-action"});let Z=h.navigation.location||h.location,ze=We(e.history,Z,T.signal),qe=d||i,xe=h.navigation.state!=="idle"?Fe(qe,h.navigation.location,u):h.matches;I(xe,"Didn't find any matches after fetcher action");let we=++ke;c.set(o,we);let wt=Ze(S,z.data);h.fetchers.set(o,wt);let[Ye,Ue]=ur(e.history,h,xe,S,Z,!1,p.v7_skipActionErrorRevalidation,ve,Ne,re,U,A,g,qe,u,[y.route.id,z]);Ue.filter(ne=>ne.key!==o).forEach(ne=>{let He=ne.key,Qt=h.fetchers.get(He),Gr=Ze(void 0,Qt?Qt.data:void 0);h.fetchers.set(He,Gr),_e(He),ne.controller&&X.set(He,ne.controller)}),fe({fetchers:new Map(h.fetchers)});let ct=()=>Ue.forEach(ne=>_e(ne.key));T.signal.addEventListener("abort",ct);let{loaderResults:dt,fetcherResults:k}=await Ht(h,xe,Ye,Ue,ze);if(T.signal.aborted)return;T.signal.removeEventListener("abort",ct),c.delete(o),X.delete(o),Ue.forEach(ne=>X.delete(ne.key));let ee=ft(dt);if(ee)return Te(ze,ee.result,!1,{preventScrollReset:P});if(ee=ft(k),ee)return g.add(ee.key),Te(ze,ee.result,!1,{preventScrollReset:P});let{loaderData:$e,errors:Ge}=pr(h,xe,dt,void 0,Ue,k,V);if(h.fetchers.has(o)){let ne=Ce(z.data);h.fetchers.set(o,ne)}Jt(we),h.navigation.state==="loading"&&we>Se?(I(K,"Expected pending action"),W&&W.abort(),Be(h.navigation.location,{matches:xe,loaderData:$e,errors:Ge,fetchers:new Map(h.fetchers)})):(fe({errors:Ge,loaderData:mr(h.loaderData,$e,xe,Ge),fetchers:new Map(h.fetchers)}),ve=!1)}async function Hr(o,s,f,y,w,C,j,P,S){let x=h.fetchers.get(o);De(o,Ze(S,x?x.data:void 0),{flushSync:j});let _=new AbortController,T=We(e.history,f,_.signal);if(C){let z=await ut(w,new URL(T.url).pathname,T.signal,o);if(z.type==="aborted")return;if(z.type==="error"){Re(o,s,z.error,{flushSync:j});return}else if(z.matches)w=z.matches,y=et(w,f);else{Re(o,s,he(404,{pathname:f}),{flushSync:j});return}}X.set(o,_);let N=ke,q=(await Je("loader",h,T,[y],w,o))[y.route.id];if(Me(q)&&(q=await Ut(q,T.signal,!0)||q),X.get(o)===_&&X.delete(o),!T.signal.aborted){if(U.has(o)){De(o,Ce(void 0));return}if(Ae(q))if(Se>N){De(o,Ce(void 0));return}else{g.add(o),await Te(T,q,!1,{preventScrollReset:P});return}if(me(q)){Re(o,s,q.error);return}I(!Me(q),"Unhandled fetcher deferred data"),De(o,Ce(q.data))}}async function Te(o,s,f,y){let{submission:w,fetcherSubmission:C,preventScrollReset:j,replace:P}=y===void 0?{}:y;s.response.headers.has("X-Remix-Revalidate")&&(ve=!0);let S=s.response.headers.get("Location");I(S,"Expected a Location header on the redirect Response"),S=fr(S,new URL(o.url),u);let x=tt(h.location,S,{_isRedirect:!0});if(r){let z=!1;if(s.response.headers.has("X-Remix-Reload-Document"))z=!0;else if(Tt.test(S)){const Z=e.history.createURL(S);z=Z.origin!==t.location.origin||at(Z.pathname,u)==null}if(z){P?t.location.replace(S):t.location.assign(S);return}}W=null;let _=P===!0||s.response.headers.has("X-Remix-Replace")?ae.Replace:ae.Push,{formMethod:T,formAction:N,formEncType:le}=h.navigation;!w&&!C&&T&&N&&le&&(w=br(h.navigation));let q=w||C;if(On.has(s.response.status)&&q&&be(q.formMethod))await Oe(_,x,{submission:G({},q,{formAction:S}),preventScrollReset:j||oe,enableViewTransition:f?ue:void 0});else{let z=St(x,w);await Oe(_,x,{overrideNavigation:z,fetcherSubmission:C,preventScrollReset:j||oe,enableViewTransition:f?ue:void 0})}}async function Je(o,s,f,y,w,C){let j,P={};try{j=await kn(m,o,s,f,y,w,C,l,a)}catch(S){return y.forEach(x=>{P[x.route.id]={type:H.error,error:S}}),P}for(let[S,x]of Object.entries(j))if(Vn(x)){let _=x.result;P[S]={type:H.redirect,response:$n(_,f,S,w,u,p.v7_relativeSplatPath)}}else P[S]=await zn(x);return P}async function Ht(o,s,f,y,w){let C=o.matches,j=Je("loader",o,w,f,s,null),P=Promise.all(y.map(async _=>{if(_.matches&&_.match&&_.controller){let N=(await Je("loader",o,We(e.history,_.path,_.controller.signal),[_.match],_.matches,_.key))[_.match.route.id];return{[_.key]:N}}else return Promise.resolve({[_.key]:{type:H.error,error:he(404,{pathname:_.path})}})})),S=await j,x=(await P).reduce((_,T)=>Object.assign(_,T),{});return await Promise.all([qn(s,S,w.signal,C,o.loaderData),Yn(s,x,y)]),{loaderResults:S,fetcherResults:x}}function yt(){ve=!0,Ne.push(...bt()),A.forEach((o,s)=>{X.has(s)&&re.add(s),_e(s)})}function De(o,s,f){f===void 0&&(f={}),h.fetchers.set(o,s),fe({fetchers:new Map(h.fetchers)},{flushSync:(f&&f.flushSync)===!0})}function Re(o,s,f,y){y===void 0&&(y={});let w=Ie(h.matches,s);it(o),fe({errors:{[w.route.id]:f},fetchers:new Map(h.fetchers)},{flushSync:(y&&y.flushSync)===!0})}function Wt(o){return B.set(o,(B.get(o)||0)+1),U.has(o)&&U.delete(o),h.fetchers.get(o)||Tn}function it(o){let s=h.fetchers.get(o);X.has(o)&&!(s&&s.state==="loading"&&c.has(o))&&_e(o),A.delete(o),c.delete(o),g.delete(o),p.v7_fetcherPersist&&U.delete(o),re.delete(o),h.fetchers.delete(o)}function Wr(o){let s=(B.get(o)||0)-1;s<=0?(B.delete(o),U.add(o),p.v7_fetcherPersist||it(o)):B.set(o,s),fe({fetchers:new Map(h.fetchers)})}function _e(o){let s=X.get(o);s&&(s.abort(),X.delete(o))}function Vt(o){for(let s of o){let f=Wt(s),y=Ce(f.data);h.fetchers.set(s,y)}}function Kt(){let o=[],s=!1;for(let f of g){let y=h.fetchers.get(f);I(y,"Expected fetcher: "+f),y.state==="loading"&&(g.delete(f),o.push(f),s=!0)}return Vt(o),s}function Jt(o){let s=[];for(let[f,y]of c)if(y<o){let w=h.fetchers.get(f);I(w,"Expected fetcher: "+f),w.state==="loading"&&(_e(f),c.delete(f),s.push(f))}return Vt(s),s.length>0}function Vr(o,s){let f=h.blockers.get(o)||Qe;return J.get(o)!==s&&J.set(o,s),f}function qt(o){h.blockers.delete(o),J.delete(o)}function lt(o,s){let f=h.blockers.get(o)||Qe;I(f.state==="unblocked"&&s.state==="blocked"||f.state==="blocked"&&s.state==="blocked"||f.state==="blocked"&&s.state==="proceeding"||f.state==="blocked"&&s.state==="unblocked"||f.state==="proceeding"&&s.state==="unblocked","Invalid blocker state transition: "+f.state+" -> "+s.state);let y=new Map(h.blockers);y.set(o,s),fe({blockers:y})}function Yt(o){let{currentLocation:s,nextLocation:f,historyAction:y}=o;if(J.size===0)return;J.size>1&&Ve(!1,"A router only supports one blocker at a time");let w=Array.from(J.entries()),[C,j]=w[w.length-1],P=h.blockers.get(C);if(!(P&&P.state==="proceeding")&&j({currentLocation:s,nextLocation:f,historyAction:y}))return C}function gt(o){let s=he(404,{pathname:o}),f=d||i,{matches:y,route:w}=yr(f);return bt(),{notFoundMatches:y,route:w,error:s}}function bt(o){let s=[];return V.forEach((f,y)=>{(!o||o(y))&&(f.cancel(),s.push(y),V.delete(y))}),s}function Kr(o,s,f){if(M=o,D=s,L=f||null,!$&&h.navigation===Pt){$=!0;let y=Xt(h.location,h.matches);y!=null&&fe({restoreScrollPosition:y})}return()=>{M=null,D=null,L=null}}function Gt(o,s){return L&&L(o,s.map(y=>Rr(y,h.loaderData)))||o.key}function Jr(o,s){if(M&&D){let f=Gt(o,s);M[f]=D()}}function Xt(o,s){if(M){let f=Gt(o,s),y=M[f];if(typeof y=="number")return y}return null}function st(o,s,f){if(v)if(o){if(Object.keys(o[0].params).length>0)return{active:!0,matches:ht(s,f,u,!0)}}else return{active:!0,matches:ht(s,f,u,!0)||[]};return{active:!1,matches:null}}async function ut(o,s,f,y){if(!v)return{type:"success",matches:o};let w=o;for(;;){let C=d==null,j=d||i,P=l;try{await v({signal:f,path:s,matches:w,fetcherKey:y,patch:(_,T)=>{f.aborted||dr(_,T,j,P,a)}})}catch(_){return{type:"error",error:_,partialMatches:w}}finally{C&&!f.aborted&&(i=[...i])}if(f.aborted)return{type:"aborted"};let S=Fe(j,s,u);if(S)return{type:"success",matches:S};let x=ht(j,s,u,!0);if(!x||w.length===x.length&&w.every((_,T)=>_.route.id===x[T].route.id))return{type:"success",matches:null};w=x}}function qr(o){l={},d=pt(o,a,void 0,l)}function Yr(o,s){let f=d==null;dr(o,s,d||i,l,a),f&&(i=[...i],fe({}))}return Ee={get basename(){return u},get future(){return p},get state(){return h},get routes(){return i},get window(){return t},initialize:Q,subscribe:Ir,enableScrollRestoration:Kr,navigate:zt,fetch:zr,revalidate:Ar,createHref:o=>e.history.createHref(o),encodeLocation:o=>e.history.encodeLocation(o),getFetcher:Wt,deleteFetcher:Wr,dispose:pe,getBlocker:Vr,deleteBlocker:qt,patchRoutes:Yr,_internalFetchControllers:X,_internalActiveDeferreds:V,_internalSetRoutes:qr},Ee}function Fn(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function Ct(e,t,r,n,a,l,i,d){let u,m;if(i){u=[];for(let p of t)if(u.push(p),p.route.id===i){m=p;break}}else u=t,m=t[t.length-1];let v=Ot(a||".",jt(u,l),at(e.pathname,r)||e.pathname,d==="path");if(a==null&&(v.search=e.search,v.hash=e.hash),(a==null||a===""||a===".")&&m){let p=Ft(v.search);if(m.route.index&&!p)v.search=v.search?v.search.replace(/^\?/,"?index&"):"?index";else if(!m.route.index&&p){let b=new URLSearchParams(v.search),E=b.getAll("index");b.delete("index"),E.filter(L=>L).forEach(L=>b.append("index",L));let M=b.toString();v.search=M?"?"+M:""}}return n&&r!=="/"&&(v.pathname=v.pathname==="/"?r:Pe([r,v.pathname])),nt(v)}function lr(e,t,r,n){if(!n||!Fn(n))return{path:r};if(n.formMethod&&!Jn(n.formMethod))return{path:r,error:he(405,{method:n.formMethod})};let a=()=>({path:r,error:he(400,{type:"invalid-body"})}),l=n.formMethod||"get",i=e?l.toUpperCase():l.toLowerCase(),d=Lr(r);if(n.body!==void 0){if(n.formEncType==="text/plain"){if(!be(i))return a();let b=typeof n.body=="string"?n.body:n.body instanceof FormData||n.body instanceof URLSearchParams?Array.from(n.body.entries()).reduce((E,M)=>{let[L,D]=M;return""+E+L+"="+D+`
`},""):String(n.body);return{path:r,submission:{formMethod:i,formAction:d,formEncType:n.formEncType,formData:void 0,json:void 0,text:b}}}else if(n.formEncType==="application/json"){if(!be(i))return a();try{let b=typeof n.body=="string"?JSON.parse(n.body):n.body;return{path:r,submission:{formMethod:i,formAction:d,formEncType:n.formEncType,formData:void 0,json:b,text:void 0}}}catch{return a()}}}I(typeof FormData=="function","FormData is not available in this environment");let u,m;if(n.formData)u=Lt(n.formData),m=n.formData;else if(n.body instanceof FormData)u=Lt(n.body),m=n.body;else if(n.body instanceof URLSearchParams)u=n.body,m=hr(u);else if(n.body==null)u=new URLSearchParams,m=new FormData;else try{u=new URLSearchParams(n.body),m=hr(u)}catch{return a()}let v={formMethod:i,formAction:d,formEncType:n&&n.formEncType||"application/x-www-form-urlencoded",formData:m,json:void 0,text:void 0};if(be(v.formMethod))return{path:r,submission:v};let p=Le(r);return t&&p.search&&Ft(p.search)&&u.append("index",""),p.search="?"+u,{path:nt(p),submission:v}}function sr(e,t,r){r===void 0&&(r=!1);let n=e.findIndex(a=>a.route.id===t);return n>=0?e.slice(0,r?n+1:n):e}function ur(e,t,r,n,a,l,i,d,u,m,v,p,b,E,M,L){let D=L?me(L[1])?L[1].error:L[1].data:void 0,$=e.createURL(t.location),O=e.createURL(a),te=r;l&&t.errors?te=sr(r,Object.keys(t.errors)[0],!0):L&&me(L[1])&&(te=sr(r,L[0]));let se=L?L[1].statusCode:void 0,ie=i&&se&&se>=400,Ee=te.filter((K,oe)=>{let{route:W}=K;if(W.lazy)return!0;if(W.loader==null)return!1;if(l)return Mt(W,t.loaderData,t.errors);if(In(t.loaderData,t.matches[oe],K)||u.some(de=>de===K.route.id))return!0;let ue=t.matches[oe],ce=K;return cr(K,G({currentUrl:$,currentParams:ue.params,nextUrl:O,nextParams:ce.params},n,{actionResult:D,actionStatus:se,defaultShouldRevalidate:ie?!1:d||$.pathname+$.search===O.pathname+O.search||$.search!==O.search||Cr(ue,ce)}))}),h=[];return p.forEach((K,oe)=>{if(l||!r.some(ge=>ge.route.id===K.routeId)||v.has(oe))return;let W=Fe(E,K.path,M);if(!W){h.push({key:oe,routeId:K.routeId,path:K.path,matches:null,match:null,controller:null});return}let ue=t.fetchers.get(oe),ce=et(W,K.path),de=!1;b.has(oe)?de=!1:m.has(oe)?(m.delete(oe),de=!0):ue&&ue.state!=="idle"&&ue.data===void 0?de=d:de=cr(ce,G({currentUrl:$,currentParams:t.matches[t.matches.length-1].params,nextUrl:O,nextParams:r[r.length-1].params},n,{actionResult:D,actionStatus:se,defaultShouldRevalidate:ie?!1:d})),de&&h.push({key:oe,routeId:K.routeId,path:K.path,matches:W,match:ce,controller:new AbortController})}),[Ee,h]}function Mt(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=t!=null&&t[e.id]!==void 0,a=r!=null&&r[e.id]!==void 0;return!n&&a?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!n&&!a}function In(e,t,r){let n=!t||r.route.id!==t.route.id,a=e[r.route.id]===void 0;return n||a}function Cr(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function cr(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function dr(e,t,r,n,a){var l;let i;if(e){let m=n[e];I(m,"No route found to patch children into: routeId = "+e),m.children||(m.children=[]),i=m.children}else i=r;let d=t.filter(m=>!i.some(v=>Mr(m,v))),u=pt(d,a,[e||"_","patch",String(((l=i)==null?void 0:l.length)||"0")],n);i.push(...u)}function Mr(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,n)=>{var a;return(a=t.children)==null?void 0:a.some(l=>Mr(r,l))}):!1}async function An(e,t,r){if(!e.lazy)return;let n=await e.lazy();if(!e.lazy)return;let a=r[e.id];I(a,"No route found in manifest");let l={};for(let i in n){let u=a[i]!==void 0&&i!=="hasErrorBoundary";Ve(!u,'Route "'+a.id+'" has a static property "'+i+'" defined but its lazy function is also returning a value for this property. '+('The lazy route property "'+i+'" will be ignored.')),!u&&!on.has(i)&&(l[i]=n[i])}Object.assign(a,l),Object.assign(a,G({},t(a),{lazy:void 0}))}async function Nn(e){let{matches:t}=e,r=t.filter(a=>a.shouldLoad);return(await Promise.all(r.map(a=>a.resolve()))).reduce((a,l,i)=>Object.assign(a,{[r[i].route.id]:l}),{})}async function kn(e,t,r,n,a,l,i,d,u,m){let v=l.map(E=>E.route.lazy?An(E.route,u,d):void 0),p=l.map((E,M)=>{let L=v[M],D=a.some(O=>O.route.id===E.route.id);return G({},E,{shouldLoad:D,resolve:async O=>(O&&n.method==="GET"&&(E.route.lazy||E.route.loader)&&(D=!0),D?Bn(t,n,E,L,O,m):Promise.resolve({type:H.data,result:void 0}))})}),b=await e({matches:p,request:n,params:l[0].params,fetcherKey:i,context:m});try{await Promise.all(v)}catch{}return b}async function Bn(e,t,r,n,a,l){let i,d,u=m=>{let v,p=new Promise((M,L)=>v=L);d=()=>v(),t.signal.addEventListener("abort",d);let b=M=>typeof m!="function"?Promise.reject(new Error("You cannot call the handler for a route which defines a boolean "+('"'+e+'" [routeId: '+r.route.id+"]"))):m({request:t,params:r.params,context:l},...M!==void 0?[M]:[]),E=(async()=>{try{return{type:"data",result:await(a?a(L=>b(L)):b())}}catch(M){return{type:"error",result:M}}})();return Promise.race([E,p])};try{let m=r.route[e];if(n)if(m){let v,[p]=await Promise.all([u(m).catch(b=>{v=b}),n]);if(v!==void 0)throw v;i=p}else if(await n,m=r.route[e],m)i=await u(m);else if(e==="action"){let v=new URL(t.url),p=v.pathname+v.search;throw he(405,{method:t.method,pathname:p,routeId:r.route.id})}else return{type:H.data,result:void 0};else if(m)i=await u(m);else{let v=new URL(t.url),p=v.pathname+v.search;throw he(404,{pathname:p})}I(i.result!==void 0,"You defined "+(e==="action"?"an action":"a loader")+" for route "+('"'+r.route.id+"\" but didn't return anything from your `"+e+"` ")+"function. Please return a value or `null`.")}catch(m){return{type:H.error,result:m}}finally{d&&t.signal.removeEventListener("abort",d)}return i}async function zn(e){let{result:t,type:r}=e;if(jr(t)){let p;try{let b=t.headers.get("Content-Type");b&&/\bapplication\/json\b/.test(b)?t.body==null?p=null:p=await t.json():p=await t.text()}catch(b){return{type:H.error,error:b}}return r===H.error?{type:H.error,error:new _t(t.status,t.statusText,p),statusCode:t.status,headers:t.headers}:{type:H.data,data:p,statusCode:t.status,headers:t.headers}}if(r===H.error){if(gr(t)){var n,a;if(t.data instanceof Error){var l,i;return{type:H.error,error:t.data,statusCode:(l=t.init)==null?void 0:l.status,headers:(i=t.init)!=null&&i.headers?new Headers(t.init.headers):void 0}}return{type:H.error,error:new _t(((n=t.init)==null?void 0:n.status)||500,void 0,t.data),statusCode:rt(t)?t.status:void 0,headers:(a=t.init)!=null&&a.headers?new Headers(t.init.headers):void 0}}return{type:H.error,error:t,statusCode:rt(t)?t.status:void 0}}if(Kn(t)){var d,u;return{type:H.deferred,deferredData:t,statusCode:(d=t.init)==null?void 0:d.status,headers:((u=t.init)==null?void 0:u.headers)&&new Headers(t.init.headers)}}if(gr(t)){var m,v;return{type:H.data,data:t.data,statusCode:(m=t.init)==null?void 0:m.status,headers:(v=t.init)!=null&&v.headers?new Headers(t.init.headers):void 0}}return{type:H.data,data:t}}function $n(e,t,r,n,a,l){let i=e.headers.get("Location");if(I(i,"Redirects returned/thrown from loaders/actions must have a Location header"),!Tt.test(i)){let d=n.slice(0,n.findIndex(u=>u.route.id===r)+1);i=Ct(new URL(t.url),d,a,!0,i,l),e.headers.set("Location",i)}return e}function fr(e,t,r){if(Tt.test(e)){let n=e,a=n.startsWith("//")?new URL(t.protocol+n):new URL(n),l=at(a.pathname,r)!=null;if(a.origin===t.origin&&l)return a.pathname+a.search+a.hash}return e}function We(e,t,r,n){let a=e.createURL(Lr(t)).toString(),l={signal:r};if(n&&be(n.formMethod)){let{formMethod:i,formEncType:d}=n;l.method=i.toUpperCase(),d==="application/json"?(l.headers=new Headers({"Content-Type":d}),l.body=JSON.stringify(n.json)):d==="text/plain"?l.body=n.text:d==="application/x-www-form-urlencoded"&&n.formData?l.body=Lt(n.formData):l.body=n.formData}return new Request(a,l)}function Lt(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,typeof n=="string"?n:n.name);return t}function hr(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function Hn(e,t,r,n,a){let l={},i=null,d,u=!1,m={},v=r&&me(r[1])?r[1].error:void 0;return e.forEach(p=>{if(!(p.route.id in t))return;let b=p.route.id,E=t[b];if(I(!Ae(E),"Cannot handle redirect results in processLoaderData"),me(E)){let M=E.error;v!==void 0&&(M=v,v=void 0),i=i||{};{let L=Ie(e,b);i[L.route.id]==null&&(i[L.route.id]=M)}l[b]=void 0,u||(u=!0,d=rt(E.error)?E.error.status:500),E.headers&&(m[b]=E.headers)}else Me(E)?(n.set(b,E.deferredData),l[b]=E.deferredData.data,E.statusCode!=null&&E.statusCode!==200&&!u&&(d=E.statusCode),E.headers&&(m[b]=E.headers)):(l[b]=E.data,E.statusCode&&E.statusCode!==200&&!u&&(d=E.statusCode),E.headers&&(m[b]=E.headers))}),v!==void 0&&r&&(i={[r[0]]:v},l[r[0]]=void 0),{loaderData:l,errors:i,statusCode:d||200,loaderHeaders:m}}function pr(e,t,r,n,a,l,i){let{loaderData:d,errors:u}=Hn(t,r,n,i);return a.forEach(m=>{let{key:v,match:p,controller:b}=m,E=l[v];if(I(E,"Did not find corresponding fetcher result"),!(b&&b.signal.aborted))if(me(E)){let M=Ie(e.matches,p==null?void 0:p.route.id);u&&u[M.route.id]||(u=G({},u,{[M.route.id]:E.error})),e.fetchers.delete(v)}else if(Ae(E))I(!1,"Unhandled fetcher revalidation redirect");else if(Me(E))I(!1,"Unhandled fetcher deferred data");else{let M=Ce(E.data);e.fetchers.set(v,M)}}),{loaderData:d,errors:u}}function mr(e,t,r,n){let a=G({},t);for(let l of r){let i=l.route.id;if(t.hasOwnProperty(i)?t[i]!==void 0&&(a[i]=t[i]):e[i]!==void 0&&l.route.loader&&(a[i]=e[i]),n&&n.hasOwnProperty(i))break}return a}function vr(e){return e?me(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function Ie(e,t){return(t?e.slice(0,e.findIndex(n=>n.route.id===t)+1):[...e]).reverse().find(n=>n.route.hasErrorBoundary===!0)||e[0]}function yr(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function he(e,t){let{pathname:r,routeId:n,method:a,type:l,message:i}=t===void 0?{}:t,d="Unknown Server Error",u="Unknown @remix-run/router error";return e===400?(d="Bad Request",a&&r&&n?u="You made a "+a+' request to "'+r+'" but '+('did not provide a `loader` for route "'+n+'", ')+"so there is no way to handle the request.":l==="defer-action"?u="defer() is not supported in actions":l==="invalid-body"&&(u="Unable to encode submission body")):e===403?(d="Forbidden",u='Route "'+n+'" does not match URL "'+r+'"'):e===404?(d="Not Found",u='No route matches URL "'+r+'"'):e===405&&(d="Method Not Allowed",a&&r&&n?u="You made a "+a.toUpperCase()+' request to "'+r+'" but '+('did not provide an `action` for route "'+n+'", ')+"so there is no way to handle the request.":a&&(u='Invalid request method "'+a.toUpperCase()+'"')),new _t(e||500,d,new Error(u),!0)}function ft(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[n,a]=t[r];if(Ae(a))return{key:n,result:a}}}function Lr(e){let t=typeof e=="string"?Le(e):e;return nt(G({},t,{hash:""}))}function Wn(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function Vn(e){return jr(e.result)&&jn.has(e.result.status)}function Me(e){return e.type===H.deferred}function me(e){return e.type===H.error}function Ae(e){return(e&&e.type)===H.redirect}function gr(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function Kn(e){let t=e;return t&&typeof t=="object"&&typeof t.data=="object"&&typeof t.subscribe=="function"&&typeof t.cancel=="function"&&typeof t.resolveData=="function"}function jr(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function Jn(e){return Ln.has(e.toLowerCase())}function be(e){return Cn.has(e.toLowerCase())}async function qn(e,t,r,n,a){let l=Object.entries(t);for(let i=0;i<l.length;i++){let[d,u]=l[i],m=e.find(b=>(b==null?void 0:b.route.id)===d);if(!m)continue;let v=n.find(b=>b.route.id===m.route.id),p=v!=null&&!Cr(v,m)&&(a&&a[m.route.id])!==void 0;Me(u)&&p&&await Ut(u,r,!1).then(b=>{b&&(t[d]=b)})}}async function Yn(e,t,r){for(let n=0;n<r.length;n++){let{key:a,routeId:l,controller:i}=r[n],d=t[a];e.find(m=>(m==null?void 0:m.route.id)===l)&&Me(d)&&(I(i,"Expected an AbortController for revalidating fetcher deferred result"),await Ut(d,i.signal,!0).then(m=>{m&&(t[a]=m)}))}}async function Ut(e,t,r){if(r===void 0&&(r=!1),!await e.deferredData.resolveData(t)){if(r)try{return{type:H.data,data:e.deferredData.unwrappedData}}catch(a){return{type:H.error,error:a}}return{type:H.data,data:e.deferredData.data}}}function Ft(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function et(e,t){let r=typeof t=="string"?Le(t).search:t.search;if(e[e.length-1].route.index&&Ft(r||""))return e[e.length-1];let n=Sr(e);return n[n.length-1]}function br(e){let{formMethod:t,formAction:r,formEncType:n,text:a,formData:l,json:i}=e;if(!(!t||!r||!n)){if(a!=null)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:a};if(l!=null)return{formMethod:t,formAction:r,formEncType:n,formData:l,json:void 0,text:void 0};if(i!==void 0)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:i,text:void 0}}}function St(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function Gn(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function Ze(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function Xn(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function Ce(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function Qn(e,t){try{let r=e.sessionStorage.getItem(_r);if(r){let n=JSON.parse(r);for(let[a,l]of Object.entries(n||{}))l&&Array.isArray(l)&&t.set(a,new Set(l||[]))}}catch{}}function Zn(e,t){if(t.size>0){let r={};for(let[n,a]of t)r[n]=[...a];try{e.sessionStorage.setItem(_r,JSON.stringify(r))}catch(n){Ve(!1,"Failed to save applied view transitions in sessionStorage ("+n+").")}}}/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function mt(){return mt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},mt.apply(this,arguments)}const It=R.createContext(null),ea=R.createContext(null),vt=R.createContext(null),Ke=R.createContext(null),At=R.createContext(null),je=R.createContext({outlet:null,matches:[],isDataRoute:!1}),Or=R.createContext(null);function Da(e,t){let{relative:r}=t===void 0?{}:t;ot()||I(!1);let{basename:n,navigator:a}=R.useContext(Ke),{hash:l,pathname:i,search:d}=aa(e,{relative:r}),u=i;return n!=="/"&&(u=i==="/"?n:Pe([n,i])),a.createHref({pathname:u,search:d,hash:l})}function ot(){return R.useContext(At)!=null}function Nt(){return ot()||I(!1),R.useContext(At).location}function Tr(e){R.useContext(Ke).static||R.useLayoutEffect(e)}function _a(){let{isDataRoute:e}=R.useContext(je);return e?pa():ta()}function ta(){ot()||I(!1);let e=R.useContext(It),{basename:t,future:r,navigator:n}=R.useContext(Ke),{matches:a}=R.useContext(je),{pathname:l}=Nt(),i=JSON.stringify(jt(a,r.v7_relativeSplatPath)),d=R.useRef(!1);return Tr(()=>{d.current=!0}),R.useCallback(function(m,v){if(v===void 0&&(v={}),!d.current)return;if(typeof m=="number"){n.go(m);return}let p=Ot(m,JSON.parse(i),l,v.relative==="path");e==null&&t!=="/"&&(p.pathname=p.pathname==="/"?t:Pe([t,p.pathname])),(v.replace?n.replace:n.push)(p,v.state,v)},[t,n,i,l,e])}const ra=R.createContext(null);function na(e){let t=R.useContext(je).outlet;return t&&R.createElement(ra.Provider,{value:e},t)}function aa(e,t){let{relative:r}=t===void 0?{}:t,{future:n}=R.useContext(Ke),{matches:a}=R.useContext(je),{pathname:l}=Nt(),i=JSON.stringify(jt(a,n.v7_relativeSplatPath));return R.useMemo(()=>Ot(e,JSON.parse(i),l,r==="path"),[e,i,l,r])}function Ca(e,t,r,n){ot()||I(!1);let{navigator:a,static:l}=R.useContext(Ke),{matches:i}=R.useContext(je),d=i[i.length-1],u=d?d.params:{};d&&d.pathname;let m=d?d.pathnameBase:"/";d&&d.route;let v=Nt(),p;p=v;let b=p.pathname||"/",E=b;if(m!=="/"){let D=m.replace(/^\//,"").split("/");E="/"+b.replace(/^\//,"").split("/").slice(D.length).join("/")}let M=!l&&r&&r.matches&&r.matches.length>0?r.matches:Fe(e,{pathname:E});return ua(M&&M.map(D=>Object.assign({},D,{params:Object.assign({},u,D.params),pathname:Pe([m,a.encodeLocation?a.encodeLocation(D.pathname).pathname:D.pathname]),pathnameBase:D.pathnameBase==="/"?m:Pe([m,a.encodeLocation?a.encodeLocation(D.pathnameBase).pathname:D.pathnameBase])})),i,r,n)}function oa(){let e=fa(),t=rt(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return R.createElement(R.Fragment,null,R.createElement("h2",null,"Unexpected Application Error!"),R.createElement("h3",{style:{fontStyle:"italic"}},t),r?R.createElement("pre",{style:a},r):null,null)}const ia=R.createElement(oa,null);class la extends R.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?R.createElement(je.Provider,{value:this.props.routeContext},R.createElement(Or.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function sa(e){let{routeContext:t,match:r,children:n}=e,a=R.useContext(It);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),R.createElement(je.Provider,{value:t},n)}function ua(e,t,r,n){var a;if(t===void 0&&(t=[]),r===void 0&&(r=null),n===void 0&&(n=null),e==null){var l;if(!r)return null;if(r.errors)e=r.matches;else if((l=n)!=null&&l.v7_partialHydration&&t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let i=e,d=(a=r)==null?void 0:a.errors;if(d!=null){let v=i.findIndex(p=>p.route.id&&(d==null?void 0:d[p.route.id])!==void 0);v>=0||I(!1),i=i.slice(0,Math.min(i.length,v+1))}let u=!1,m=-1;if(r&&n&&n.v7_partialHydration)for(let v=0;v<i.length;v++){let p=i[v];if((p.route.HydrateFallback||p.route.hydrateFallbackElement)&&(m=v),p.route.id){let{loaderData:b,errors:E}=r,M=p.route.loader&&b[p.route.id]===void 0&&(!E||E[p.route.id]===void 0);if(p.route.lazy||M){u=!0,m>=0?i=i.slice(0,m+1):i=[i[0]];break}}}return i.reduceRight((v,p,b)=>{let E,M=!1,L=null,D=null;r&&(E=d&&p.route.id?d[p.route.id]:void 0,L=p.route.errorElement||ia,u&&(m<0&&b===0?(ma("route-fallback"),M=!0,D=null):m===b&&(M=!0,D=p.route.hydrateFallbackElement||null)));let $=t.concat(i.slice(0,b+1)),O=()=>{let te;return E?te=L:M?te=D:p.route.Component?te=R.createElement(p.route.Component,null):p.route.element?te=p.route.element:te=v,R.createElement(sa,{match:p,routeContext:{outlet:v,matches:$,isDataRoute:r!=null},children:te})};return r&&(p.route.ErrorBoundary||p.route.errorElement||b===0)?R.createElement(la,{location:r.location,revalidation:r.revalidation,component:L,error:E,children:O(),routeContext:{outlet:null,matches:$,isDataRoute:!0}}):O()},null)}var Ur=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Ur||{}),Fr=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Fr||{});function ca(e){let t=R.useContext(It);return t||I(!1),t}function kt(e){let t=R.useContext(ea);return t||I(!1),t}function da(e){let t=R.useContext(je);return t||I(!1),t}function Bt(e){let t=da(),r=t.matches[t.matches.length-1];return r.route.id||I(!1),r.route.id}function Ma(){return Bt()}function La(){return kt().navigation}function ja(){let{matches:e,loaderData:t}=kt();return R.useMemo(()=>e.map(r=>Rr(r,t)),[e,t])}function fa(){var e;let t=R.useContext(Or),r=kt(Fr.UseRouteError),n=Bt();return t!==void 0?t:(e=r.errors)==null?void 0:e[n]}function ha(){let e=R.useContext(vt);return e==null?void 0:e._data}function Oa(){let e=R.useContext(vt);return e==null?void 0:e._error}function pa(){let{router:e}=ca(Ur.UseNavigateStable),t=Bt(),r=R.useRef(!1);return Tr(()=>{r.current=!0}),R.useCallback(function(a,l){l===void 0&&(l={}),r.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,mt({fromRouteId:t},l)))},[e,t])}const wr={};function ma(e,t,r){wr[e]||(wr[e]=!0)}function Ta(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function Ua(e){return na(e.context)}function Fa(e){let{basename:t="/",children:r=null,location:n,navigationType:a=ae.Pop,navigator:l,static:i=!1,future:d}=e;ot()&&I(!1);let u=t.replace(/^\/*/,"/"),m=R.useMemo(()=>({basename:u,navigator:l,static:i,future:mt({v7_relativeSplatPath:!1},d)}),[u,d,l,i]);typeof n=="string"&&(n=Le(n));let{pathname:v="/",search:p="",hash:b="",state:E=null,key:M="default"}=n,L=R.useMemo(()=>{let D=at(v,u);return D==null?null:{location:{pathname:D,search:p,hash:b,state:E,key:M},navigationType:a}},[u,v,p,b,E,M,a]);return L==null?null:R.createElement(Ke.Provider,{value:m},R.createElement(At.Provider,{children:r,value:L}))}function Ia(e){let{children:t,errorElement:r,resolve:n}=e;return R.createElement(ya,{resolve:n,errorElement:r},R.createElement(ga,null,t))}var ye=function(e){return e[e.pending=0]="pending",e[e.success=1]="success",e[e.error=2]="error",e}(ye||{});const va=new Promise(()=>{});class ya extends R.Component{constructor(t){super(t),this.state={error:null}}static getDerivedStateFromError(t){return{error:t}}componentDidCatch(t,r){console.error("<Await> caught the following error during render",t,r)}render(){let{children:t,errorElement:r,resolve:n}=this.props,a=null,l=ye.pending;if(!(n instanceof Promise))l=ye.success,a=Promise.resolve(),Object.defineProperty(a,"_tracked",{get:()=>!0}),Object.defineProperty(a,"_data",{get:()=>n});else if(this.state.error){l=ye.error;let i=this.state.error;a=Promise.reject().catch(()=>{}),Object.defineProperty(a,"_tracked",{get:()=>!0}),Object.defineProperty(a,"_error",{get:()=>i})}else n._tracked?(a=n,l="_error"in a?ye.error:"_data"in a?ye.success:ye.pending):(l=ye.pending,Object.defineProperty(n,"_tracked",{get:()=>!0}),a=n.then(i=>Object.defineProperty(n,"_data",{get:()=>i}),i=>Object.defineProperty(n,"_error",{get:()=>i})));if(l===ye.error&&a._error instanceof Dt)throw va;if(l===ye.error&&!r)throw a._error;if(l===ye.error)return R.createElement(vt.Provider,{value:a,children:r});if(l===ye.success)return R.createElement(vt.Provider,{value:a,children:t});throw a}}function ga(e){let{children:t}=e,r=ha(),n=typeof t=="function"?t(r):t;return R.createElement(R.Fragment,null,n)}function Aa(e){let t={hasErrorBoundary:e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&Object.assign(t,{element:R.createElement(e.Component),Component:void 0}),e.HydrateFallback&&Object.assign(t,{hydrateFallbackElement:R.createElement(e.HydrateFallback),HydrateFallback:void 0}),e.ErrorBoundary&&Object.assign(t,{errorElement:R.createElement(e.ErrorBoundary),ErrorBoundary:void 0}),t}export{Dt as A,xa as B,rt as C,It as D,_t as E,Ra as F,Pa as G,fa as H,Ia as I,Oa as J,Ke as N,Ua as O,Fa as R,Ea as a,Aa as b,Sa as c,ja as d,Er as e,ea as f,Qr as g,wa as h,Ca as i,ba as j,La as k,Ta as l,Fe as m,I as n,aa as o,Da as p,ir as q,R as r,at as s,_a as t,Nt as u,nt as v,Ma as w,je as x,Pe as y,Le as z};
