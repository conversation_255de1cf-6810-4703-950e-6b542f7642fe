import{E as h,m as M,c as y,a as g,r,b as E,j as v}from"./index-n_C3wGZ6.js";import{i as S,d as b,c as F,s as P,g as D,a as k,b as z,u as H,R as L,e as O,f as T,r as j}from"./components-7kl9B-Eh.js";import{a as p}from"./analytics-COkimW_x.js";/**
 * @remix-run/react v2.16.8
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function B(n){if(!n)return null;let x=Object.entries(n),u={};for(let[a,e]of x)if(e&&e.__type==="RouteErrorResponse")u[a]=new h(e.status,e.statusText,e.data,e.internal===!0);else if(e&&e.__type==="Error"){if(e.__subType){let o=window[e.__subType];if(typeof o=="function")try{let i=new o(e.message);i.stack=e.stack,u[a]=i}catch{}}if(u[a]==null){let o=new Error(e.message);o.stack=e.stack,u[a]=o}}else u[a]=e;return u}/**
 * @remix-run/react v2.16.8
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */let s,t,f=!1,R;new Promise(n=>{R=n}).catch(()=>{});function q(n){if(!t){if(window.__remixContext.future.v3_singleFetch){if(!s){let d=window.__remixContext.stream;S(d,"No stream found for single fetch decoding"),window.__remixContext.stream=void 0,s=b(d,window).then(l=>{window.__remixContext.state=l.value,s.value=!0}).catch(l=>{s.error=l})}if(s.error)throw s.error;if(!s.value)throw s}let o=F(window.__remixManifest.routes,window.__remixRouteModules,window.__remixContext.state,window.__remixContext.future,window.__remixContext.isSpaMode),i;if(!window.__remixContext.isSpaMode){i={...window.__remixContext.state,loaderData:{...window.__remixContext.state.loaderData}};let d=M(o,window.location,window.__remixContext.basename);if(d)for(let l of d){let _=l.route.id,c=window.__remixRouteModules[_],w=window.__remixManifest.routes[_];c&&P(w,c,window.__remixContext.isSpaMode)&&(c.HydrateFallback||!w.hasLoader)?i.loaderData[_]=void 0:w&&!w.hasLoader&&(i.loaderData[_]=null)}i&&i.errors&&(i.errors=B(i.errors))}t=y({routes:o,history:g(),basename:window.__remixContext.basename,future:{v7_normalizeFormMethod:!0,v7_fetcherPersist:window.__remixContext.future.v3_fetcherPersist,v7_partialHydration:!0,v7_prependBasename:!0,v7_relativeSplatPath:window.__remixContext.future.v3_relativeSplatPath,v7_skipActionErrorRevalidation:window.__remixContext.future.v3_singleFetch===!0},hydrationData:i,mapRouteProperties:E,dataStrategy:window.__remixContext.future.v3_singleFetch&&!window.__remixContext.isSpaMode?k(window.__remixManifest,window.__remixRouteModules,()=>t):void 0,patchRoutesOnNavigation:D(window.__remixManifest,window.__remixRouteModules,window.__remixContext.future,window.__remixContext.isSpaMode,window.__remixContext.basename)}),t.state.initialized&&(f=!0,t.initialize()),t.createRoutesForHMR=z,window.__remixRouter=t,R&&R(t)}let[x,u]=r.useState(void 0),[a,e]=r.useState(t.state.location);return r.useLayoutEffect(()=>{f||(f=!0,t.initialize())},[]),r.useLayoutEffect(()=>t.subscribe(o=>{o.location!==a&&e(o.location)}),[a]),H(t,window.__remixManifest,window.__remixRouteModules,window.__remixContext.future,window.__remixContext.isSpaMode),r.createElement(r.Fragment,null,r.createElement(L.Provider,{value:{manifest:window.__remixManifest,routeModules:window.__remixRouteModules,future:window.__remixContext.future,criticalCss:x,isSpaMode:window.__remixContext.isSpaMode}},r.createElement(O,{location:a},r.createElement(T,{router:t,fallbackElement:null,future:{v7_startTransition:!0}}))),window.__remixContext.future.v3_singleFetch?r.createElement(r.Fragment,null):null)}var m={},C;function I(){if(C)return m;C=1;var n=j();return m.createRoot=n.createRoot,m.hydrateRoot=n.hydrateRoot,m}var N=I();typeof window<"u"&&p&&p.page();r.startTransition(()=>{N.hydrateRoot(document,v.jsx(r.StrictMode,{children:v.jsx(q,{})}))});
